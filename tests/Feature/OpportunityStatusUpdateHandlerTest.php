<?php

use App\Services\FreshcarApi\IFreshcarApiRepository;
use App\Services\GoHighLevel\WebhookHandlers\OpportunityStatusUpdateHandler;
use App\Services\WebhookActivityService;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookClient\Models\WebhookCall;

beforeEach(function () {
    $this->mockRepository = \Mockery::mock(IFreshcarApiRepository::class);
    $this->app->instance(IFreshcarApiRepository::class, $this->mockRepository);

    $this->mockWebhookActivityService = \Mockery::mock(WebhookActivityService::class);
    $this->app->instance(WebhookActivityService::class, $this->mockWebhookActivityService);
});

describe('OpportunityStatusUpdateHandler', function () {
    test('processes won opportunity for new customer successfully', function () {
        $webhookPayload = json_encode([
            'contact_id' => 'mYTqcN6I8esiYUgLSqSI',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'status' => 'won',
            'opportunity_name' => 'Car Service',
            'lead_value' => 100,
            'location' => [
                'fullAddress' => '123 Main St, London'
            ]
        ]);

        $webhookCall = new WebhookCall([
            'name' => 'gohighlevel-opportunity-status-update',
            'payload' => $webhookPayload
        ]);

        // Mock Log expectations
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('info')->atLeast()->once();

        // Mock WebhookActivityService expectations
        $this->mockWebhookActivityService->shouldReceive('logWebhookReceived')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingStarted')->once();
        $this->mockWebhookActivityService->shouldReceive('logEmailCheck')->once();
        $this->mockWebhookActivityService->shouldReceive('log')->once();
        $this->mockWebhookActivityService->shouldReceive('logBookingCreated')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingCompleted')->once();

        // Mock email availability check - true means new customer
        $this->mockRepository->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andReturn(true);

        // Mock new customer booking creation
        $this->mockRepository->shouldReceive('newCustomerBooking')
            ->once()
            ->with(\Mockery::on(function ($data) {
                return $data['email'] === '<EMAIL>'
                       && $data['status'] === 'won'
                       && $data['first_name'] === 'John';
            }))
            ->andReturn(['bookingId' => 123]);

        $handler = new OpportunityStatusUpdateHandler($webhookCall);

        $handler->handle();

        // Verify the handler completed without throwing exceptions
        expect(true)->toBeTrue();
    });

    test('processes won opportunity for existing customer successfully', function () {
        $webhookPayload = json_encode([
            'email' => '<EMAIL>',
            'status' => 'won',
            'opportunity_name' => 'Repeat Service',
            'first_name' => 'Jane',
            'last_name' => 'Smith'
        ]);

        $webhookCall = new WebhookCall([
            'name' => 'gohighlevel-opportunity-status-update',
            'payload' => $webhookPayload
        ]);

        // Mock Log expectations
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('info')->atLeast()->once();

        // Mock WebhookActivityService expectations
        $this->mockWebhookActivityService->shouldReceive('logWebhookReceived')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingStarted')->once();
        $this->mockWebhookActivityService->shouldReceive('logEmailCheck')->once();
        $this->mockWebhookActivityService->shouldReceive('log')->once();
        $this->mockWebhookActivityService->shouldReceive('logBookingCreated')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingCompleted')->once();

        // Mock email availability check - false means existing customer
        $this->mockRepository->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andReturn(false);

        // Mock previous customer booking creation
        $this->mockRepository->shouldReceive('previousCustomerBooking')
            ->once()
            ->with(\Mockery::on(function ($data) {
                return $data['email'] === '<EMAIL>'
                       && $data['status'] === 'won';
            }))
            ->andReturn(['bookingId' => 456]);

        $handler = new OpportunityStatusUpdateHandler($webhookCall);

        $handler->handle();

        expect(true)->toBeTrue();
    });

    test('skips processing when status is not won', function () {
        $webhookPayload = json_encode([
            'email' => '<EMAIL>',
            'status' => 'open',
            'opportunity_name' => 'Pending Service'
        ]);

        $webhookCall = new WebhookCall([
            'name' => 'gohighlevel-opportunity-status-update',
            'payload' => $webhookPayload
        ]);

        // Mock Log expectations
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('info')->with('webhook:gohighlevel:opportunity-status-handler:received-payload:', \Mockery::any());
        Log::shouldReceive('info')->with('webhook:gohighlevel:opportunity-status-handler:skipping - status not won', [
            'status' => 'open'
        ]);

        // Mock WebhookActivityService expectations - only the initial ones
        $this->mockWebhookActivityService->shouldReceive('logWebhookReceived')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingStarted')->once();

        // Should not call any booking methods
        $this->mockRepository->shouldNotReceive('emailAvailable');
        $this->mockRepository->shouldNotReceive('newCustomerBooking');
        $this->mockRepository->shouldNotReceive('previousCustomerBooking');

        $handler = new OpportunityStatusUpdateHandler($webhookCall);

        $handler->handle();

        expect(true)->toBeTrue();
    });

    test('skips processing when email is missing', function () {
        $webhookPayload = json_encode([
            'status' => 'won',
            'opportunity_name' => 'Service Without Email'
            // Missing email field
        ]);

        $webhookCall = new WebhookCall([
            'name' => 'gohighlevel-opportunity-status-update',
            'payload' => $webhookPayload
        ]);

        // Mock Log expectations
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('info')->with('webhook:gohighlevel:opportunity-status-handler:received-payload:', \Mockery::any());
        Log::shouldReceive('error')->with('webhook:gohighlevel:opportunity-status-handler:missing-email', \Mockery::any());

        // Mock WebhookActivityService expectations - only the initial ones
        $this->mockWebhookActivityService->shouldReceive('logWebhookReceived')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingStarted')->once();

        // Should not call any booking methods
        $this->mockRepository->shouldNotReceive('emailAvailable');
        $this->mockRepository->shouldNotReceive('newCustomerBooking');
        $this->mockRepository->shouldNotReceive('previousCustomerBooking');

        $handler = new OpportunityStatusUpdateHandler($webhookCall);

        $handler->handle();

        expect(true)->toBeTrue();
    });

    test('handles email availability check failure', function () {
        $webhookPayload = json_encode([
            'email' => '<EMAIL>',
            'status' => 'won',
            'opportunity_name' => 'Error Test'
        ]);

        $webhookCall = new WebhookCall([
            'name' => 'gohighlevel-opportunity-status-update',
            'payload' => $webhookPayload
        ]);

        // Mock Log expectations
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('error')->with('webhook:gohighlevel:opportunity-status-handler:email-check-failed', [
            'email' => '<EMAIL>'
        ]);

        // Mock WebhookActivityService expectations - only the initial ones
        $this->mockWebhookActivityService->shouldReceive('logWebhookReceived')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingStarted')->once();

        // Mock email availability check returning null (error)
        $this->mockRepository->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andReturn(null);

        // Should not call any booking methods
        $this->mockRepository->shouldNotReceive('newCustomerBooking');
        $this->mockRepository->shouldNotReceive('previousCustomerBooking');

        $handler = new OpportunityStatusUpdateHandler($webhookCall);

        $handler->handle();

        expect(true)->toBeTrue();
    });

    test('handles array payload format from examples/customwebhook.json', function () {
        // Test with array format as shown in examples/customwebhook.json
        $webhookPayload = json_encode([
            [
                'contact_id' => 'mYTqcN6I8esiYUgLSqSI',
                'first_name' => 'Luk',
                'last_name' => '3',
                'email' => '<EMAIL>',
                'status' => 'won',
                'opportunity_name' => 'Luk3',
                'lead_value' => 2
            ],
            [
                'Spatie\\WebhookClient\\WebhookConfig' => [
                    'name' => 'gohighlevel-webhook-default'
                ]
            ]
        ]);

        $webhookCall = new WebhookCall([
            'name' => 'gohighlevel-opportunity-status-update',
            'payload' => $webhookPayload
        ]);

        // Mock Log expectations
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('info')->atLeast()->once();

        // Mock WebhookActivityService expectations
        $this->mockWebhookActivityService->shouldReceive('logWebhookReceived')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingStarted')->once();
        $this->mockWebhookActivityService->shouldReceive('logEmailCheck')->once();
        $this->mockWebhookActivityService->shouldReceive('log')->once();
        $this->mockWebhookActivityService->shouldReceive('logBookingCreated')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingCompleted')->once();

        $this->mockRepository->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andReturn(true);

        $this->mockRepository->shouldReceive('newCustomerBooking')
            ->once()
            ->andReturn(['bookingId' => 789]);

        $handler = new OpportunityStatusUpdateHandler($webhookCall);

        $handler->handle();

        expect(true)->toBeTrue();
    });

    test('handles malformed JSON payload gracefully', function () {
        $webhookCall = new WebhookCall([
            'name' => 'gohighlevel-opportunity-status-update',
            'payload' => 'invalid json{'
        ]);

        // Mock Log expectations
        Log::shouldReceive('channel')->with('webhook')->andReturnSelf();
        Log::shouldReceive('info')->with('webhook:gohighlevel:opportunity-status-handler:received-payload:', ['payload' => []]);
        Log::shouldReceive('info')->with('webhook:gohighlevel:opportunity-status-handler:skipping - status not won', [
            'status' => 'unknown'
        ]);

        // Mock WebhookActivityService expectations - only the initial ones
        $this->mockWebhookActivityService->shouldReceive('logWebhookReceived')->once();
        $this->mockWebhookActivityService->shouldReceive('logProcessingStarted')->once();

        $handler = new OpportunityStatusUpdateHandler($webhookCall);

        $handler->handle();

        expect(true)->toBeTrue();
    });
});
