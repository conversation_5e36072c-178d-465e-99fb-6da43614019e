<?php

use App\Services\FreshcarApi\FreshcarApiRepository;
use App\Services\FreshcarApi\IFreshCarApiClient;

beforeEach(function () {
    $this->mockClient = \Mockery::mock(IFreshCarApiClient::class);
    $this->repository = new FreshcarApiRepository($this->mockClient);
});

describe('GoHighLevel Data Transformation', function () {
    test('transforms complete webhook data correctly', function () {
        // Use data structure from examples/customwebhook.json
        $webhookData = [
            'contact_id' => 'mYTqcN6I8esiYUgLSqSI',
            'first_name' => 'Luk',
            'last_name' => '3',
            'full_name' => 'Luk 3',
            'email' => '<EMAIL>',
            'tags' => 'submitted with no photos',
            'country' => 'GB',
            'date_created' => '2025-06-02T08:48:24.571Z',
            'full_address' => null,
            'contact_type' => 'lead',
            'opportunity_name' => 'Luk3',
            'status' => 'won',
            'lead_value' => 2,
            'opportunity_source' => 'undefined',
            'source' => 'undefined',
            'pipleline_stage' => 'booked',
            'pipeline_id' => 'iy8yb8BBsbY1WiqzlcQ3',
            'id' => 'VOn9DcpHfTGo5iGwYHdH',
            'pipeline_name' => 'Smart Repair',
            'user' => [
                'firstName' => 'Morlene',
                'lastName' => 'Lightning',
                'email' => '<EMAIL>'
            ],
            'owner' => 'Morlene Lightning',
            'location' => [
                'name' => 'Test Account',
                'address' => '29 Stafford Street',
                'city' => 'Edinburgh',
                'state' => null,
                'country' => 'GB',
                'postalCode' => 'EH3 7BJ',
                'fullAddress' => '29 Stafford Street, Edinburgh EH3 7BJ',
                'id' => 'SiOmQ4U4fRp5dvSKYoLe'
            ]
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        // Verify basic customer information
        expect($result['registerDTO']['firstName'])->toBe('Luk');
        expect($result['registerDTO']['lastName'])->toBe('3');
        expect($result['registerDTO']['email'])->toBe('<EMAIL>');
        expect($result['country'])->toBe('GB');
        expect($result['fullAddress'])->toBe('29 Stafford Street, Edinburgh EH3 7BJ');

        // Verify booking information
        expect($result['additionalComments'])->toBe('submitted with no photos');
        expect($result['notes'])->toBe('Booking created from GoHighLevel opportunity: Luk3');
        expect($result['totalCost'])->toBe(2);
        expect($result['resourceName'])->toBe('Morlene Lightning');

        // Verify booking reference format
        expect($result['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);

        // Verify required fields are present
        expect($result)->toHaveKeys([
            'bookingId',
            'requestedDate',
            'confirmedDate',
            'addressId',
            'preferredTime',
            'bookingStatusId',
            'enquiryStatus',
            'resourceId',
            'timeOfDay',
            'bookingHubDurationMinutes',
            'overridePrice',
            'bookingCustomerCars',
            'isPrepaid',
            'isRefunded',
            'customerCarClubPackageId',
            'currency'
        ]);

        // Verify default values
        expect($result['bookingId'])->toBe(0);
        expect($result['addressId'])->toBe(0);
        expect($result['bookingStatusId'])->toBe(0);
        expect($result['enquiryStatus'])->toBe('pending');
        expect($result['resourceId'])->toBe(0);
        expect($result['timeOfDay'])->toBe('morning');
        expect($result['bookingHubDurationMinutes'])->toBe('60');
        expect($result['overridePrice'])->toBe(0);
        expect($result['bookingCustomerCars'])->toBe([]);
        expect($result['isPrepaid'])->toBeFalse();
        expect($result['isRefunded'])->toBeFalse();
        expect($result['customerCarClubPackageId'])->toBe(0);
        expect($result['currency'])->toBe(0);

        // Verify dates are ISO strings (allowing for microseconds)
        expect($result['requestedDate'])->toMatch('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z$/');
        expect($result['confirmedDate'])->toMatch('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z$/');
        expect($result['preferredTime'])->toMatch('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z$/');
    });

    test('handles minimal webhook data with defaults', function () {
        $webhookData = [
            'id' => 'minimal-test',
            'email' => '<EMAIL>'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['firstName'])->toBe('');
        expect($result['lastName'])->toBe('');
        expect($result['email'])->toBe('<EMAIL>');
        expect($result['country'])->toBe('GB'); // Default
        expect($result['fullAddress'])->toBe('');
        expect($result['additionalComments'])->toBe('');
        expect($result['notes'])->toBe('Booking created from GoHighLevel opportunity: Unknown');
        expect($result['totalCost'])->toBe(0);
        expect($result['resourceName'])->toBe('');

        // Should still generate booking reference
        expect($result['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);
    });

    test('generates unique booking reference numbers', function () {
        $webhookData1 = ['id' => 'test1', 'email' => '<EMAIL>'];
        $webhookData2 = ['id' => 'test2', 'email' => '<EMAIL>'];

        $result1 = $this->repository->transformGoHighLevelData($webhookData1);
        $result2 = $this->repository->transformGoHighLevelData($webhookData2);

        expect($result1['bookingReferenceNumber'])
            ->not->toBe($result2['bookingReferenceNumber']);
    });

    test('handles null location gracefully', function () {
        $webhookData = [
            'id' => 'no-location',
            'email' => '<EMAIL>',
            'location' => null
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['fullAddress'])->toBe('');
    });

    test('handles missing location fullAddress', function () {
        $webhookData = [
            'id' => 'no-full-address',
            'email' => '<EMAIL>',
            'location' => [
                'name' => 'Test Location',
                'city' => 'London'
                // Missing fullAddress
            ]
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['fullAddress'])->toBe('');
    });

    test('preserves numeric lead value correctly', function () {
        $webhookData = [
            'id' => 'numeric-test',
            'email' => '<EMAIL>',
            'lead_value' => 250.50
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['totalCost'])->toBe(250.50);
    });

    test('handles string lead value', function () {
        $webhookData = [
            'id' => 'string-test',
            'email' => '<EMAIL>',
            'lead_value' => '150'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['totalCost'])->toBe('150');
    });

    test('booking reference uses webhook id when available', function () {
        $webhookData = [
            'id' => 'VOn9DcpHfTGo5iGwYHdH',
            'email' => '<EMAIL>'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        // Should use the webhook ID in the hash
        expect($result['bookingReferenceNumber'])->toStartWith('GHL-');

        // Generate the same data again to verify consistency
        $result2 = $this->repository->transformGoHighLevelData($webhookData);
        expect($result['bookingReferenceNumber'])->toBe($result2['bookingReferenceNumber']);
    });

    test('booking reference handles missing id', function () {
        $webhookData = [
            'email' => '<EMAIL>'
            // Missing id
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        // Should still generate a reference number
        expect($result['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);
    });
});
