<?php

use App\Services\FreshcarApi\FreshcarApiRepository;
use App\Services\FreshcarApi\IFreshCarApiClient;

beforeEach(function () {
    $this->mockClient = \Mockery::mock(IFreshCarApiClient::class);

    config(['services.freshcarapi' => [
        'admin_user' => 'test_admin',
        'admin_password' => 'test_password'
    ]]);

    $this->mockClient->shouldReceive('authenticateAsUser')
        ->with('test_admin', 'test_password')
        ->andReturn(true);

    $this->mockClient->shouldReceive('adminGetAllValeters')
        ->andReturn(['payload' => []]);

    $this->mockClient->shouldReceive('logout')->andReturn(true);

    $this->repository = new FreshcarApiRepository($this->mockClient);
});

describe('GoHighLevel Data Transformation', function () {
    test('transforms complete webhook data correctly', function () {
        // Use data structure from examples/customwebhook.json
        $webhookData = [
            'contact_id' => 'mYTqcN6I8esiYUgLSqSI',
            'first_name' => 'Luk',
            'last_name' => '3',
            'full_name' => 'Luk 3',
            'email' => '<EMAIL>',
            'tags' => 'submitted with no photos',
            'country' => 'GB',
            'date_created' => '2025-06-02T08:48:24.571Z',
            'full_address' => null,
            'contact_type' => 'lead',
            'opportunity_name' => 'Luk3',
            'status' => 'won',
            'lead_value' => 2,
            'opportunity_source' => 'undefined',
            'source' => 'undefined',
            'pipleline_stage' => 'booked',
            'pipeline_id' => 'iy8yb8BBsbY1WiqzlcQ3',
            'id' => 'VOn9DcpHfTGo5iGwYHdH',
            'pipeline_name' => 'Smart Repair',
            'user' => [
                'firstName' => 'Morlene',
                'lastName' => 'Lightning',
                'email' => '<EMAIL>'
            ],
            'owner' => 'Morlene Lightning',
            'location' => [
                'name' => 'Test Account',
                'address' => '29 Stafford Street',
                'city' => 'Edinburgh',
                'state' => null,
                'country' => 'GB',
                'postalCode' => 'EH3 7BJ',
                'fullAddress' => '29 Stafford Street, Edinburgh EH3 7BJ',
                'id' => 'SiOmQ4U4fRp5dvSKYoLe'
            ]
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        // Verify basic customer information
        expect($result['registerDTO']['firstName'])->toBe('Luk');
        expect($result['registerDTO']['lastName'])->toBe('3');
        expect($result['registerDTO']['email'])->toBe('<EMAIL>');
        expect($result['bookingDTO']['country'])->toBe(0);

        // Verify booking information
        expect($result['bookingDTO']['additionalComments'])->toBe('submitted with no photos');
        expect($result['bookingDTO']['notes'])->toBe('Booking created from GoHighLevel opportunity: Luk3');
        expect($result['bookingDTO']['totalCost'])->toBe(0);
        expect($result['bookingDTO']['resourceName'])->toBe('');

        // Verify booking reference format
        expect($result['bookingDTO']['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);

        expect($result)->toHaveKeys([
            'registerDTO',
            'bookingDTO',
            'paymentDetails'
        ]);

        expect($result['registerDTO'])->toHaveKeys([
            'email',
            'password',
            'confirmPassword',
            'firstName',
            'lastName',
            'contactNumber',
            'address'
        ]);

        expect($result['bookingDTO'])->toHaveKeys([
            'requestedDate',
            'confirmedDate',
            'additionalComments',
            'bookingStatusId',
            'currency',
            'bookingCustomerCars',
            'notes',
            'preferredTime',
            'totalCost',
            'enquiryStatus',
            'resourceId',
            'resourceName',
            'bookingReferenceNumber',
            'bookingHubDurationMinutes',
            'overridePrice',
            'country'
        ]);

        expect($result['bookingDTO']['bookingStatusId'])->toBe(2);
        expect($result['bookingDTO']['enquiryStatus'])->toBe('confirmed');
        expect($result['bookingDTO']['resourceId'])->toBe(0);
        expect($result['bookingDTO']['bookingHubDurationMinutes'])->toBe(60);
        expect($result['bookingDTO']['overridePrice'])->toBe(0);
        expect($result['bookingDTO']['bookingCustomerCars'])->toBeArray();
        expect($result['paymentDetails']['isPayNow'])->toBeFalse();
        expect($result['bookingDTO']['currency'])->toBe(0);

        // Verify dates are ISO strings (allowing for microseconds)
        expect($result['bookingDTO']['requestedDate'])->toMatch('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z$/');
        expect($result['bookingDTO']['confirmedDate'])->toMatch('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z$/');
        expect($result['bookingDTO']['preferredTime'])->toMatch('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z$/');
    });

    test('handles minimal webhook data with defaults', function () {
        $webhookData = [
            'id' => 'minimal-test',
            'email' => '<EMAIL>'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['registerDTO']['firstName'])->toBe('');
        expect($result['registerDTO']['lastName'])->toBe('');
        expect($result['registerDTO']['email'])->toBe('<EMAIL>');
        expect($result['bookingDTO']['country'])->toBe(0); // Default GB enum value
        expect($result['bookingDTO']['additionalComments'])->toBe('');
        expect($result['bookingDTO']['notes'])->toBe('Booking created from GoHighLevel opportunity: Unknown');
        expect($result['bookingDTO']['totalCost'])->toBe(0);
        expect($result['bookingDTO']['resourceName'])->toBe('');

        // Should still generate booking reference
        expect($result['bookingDTO']['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);
    });

    test('generates unique booking reference numbers', function () {
        $webhookData1 = ['id' => 'test1', 'email' => '<EMAIL>'];
        $webhookData2 = ['id' => 'test2', 'email' => '<EMAIL>'];

        $result1 = $this->repository->transformGoHighLevelData($webhookData1);
        $result2 = $this->repository->transformGoHighLevelData($webhookData2);

        expect($result1['bookingDTO']['bookingReferenceNumber'])
            ->not->toBe($result2['bookingDTO']['bookingReferenceNumber']);
    });

    test('handles null location gracefully', function () {
        $webhookData = [
            'id' => 'no-location',
            'email' => '<EMAIL>',
            'location' => null
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result)->toHaveKeys(['registerDTO', 'bookingDTO', 'paymentDetails']);
        expect($result['registerDTO']['email'])->toBe('<EMAIL>');
        expect($result['registerDTO']['firstName'])->toBe('');
        expect($result['registerDTO']['lastName'])->toBe('');

        expect($result['bookingDTO']['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);

        expect($result['bookingDTO']['country'])->toBe(0);
        expect($result['bookingDTO']['notes'])->toBe('Booking created from GoHighLevel opportunity: Unknown');
    });

    test('handles missing location fullAddress', function () {
        $webhookData = [
            'id' => 'no-full-address',
            'email' => '<EMAIL>',
            'location' => [
                'name' => 'Test Location',
                'city' => 'London'
                // Missing fullAddress
            ]
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result)->toHaveKeys(['registerDTO', 'bookingDTO', 'paymentDetails']);
        expect($result['registerDTO']['email'])->toBe('<EMAIL>');
        expect($result['registerDTO']['firstName'])->toBe('');
        expect($result['registerDTO']['lastName'])->toBe('');

        expect($result['bookingDTO']['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);

        expect($result['bookingDTO']['country'])->toBe(0);
        expect($result['bookingDTO']['notes'])->toBe('Booking created from GoHighLevel opportunity: Unknown');

        expect($result['registerDTO']['address'])->toBeArray();
    });

    test('preserves numeric lead value correctly', function () {
        $webhookData = [
            'id' => 'numeric-test',
            'email' => '<EMAIL>',
            'lead_value' => 250.50
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['bookingDTO']['totalCost'])->toBe(0);
    });

    test('handles string lead value', function () {
        $webhookData = [
            'id' => 'string-test',
            'email' => '<EMAIL>',
            'lead_value' => '150'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['bookingDTO']['totalCost'])->toBe(0);
    });

    test('booking reference uses webhook id when available', function () {
        $webhookData = [
            'id' => 'VOn9DcpHfTGo5iGwYHdH',
            'email' => '<EMAIL>'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        // Should use the webhook ID in the hash
        expect($result['bookingDTO']['bookingReferenceNumber'])->toStartWith('GHL-');

        // Generate the same data again to verify consistency
        $result2 = $this->repository->transformGoHighLevelData($webhookData);
        expect($result['bookingDTO']['bookingReferenceNumber'])->toBe($result2['bookingDTO']['bookingReferenceNumber']);
    });

    test('booking reference handles missing id', function () {
        $webhookData = [
            'email' => '<EMAIL>'
            // Missing id
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        // Should still generate a reference number
        expect($result['bookingDTO']['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);
    });

    test('handles service duration in H:i format', function () {
        $webhookData = [
            'id' => 'duration-test',
            'email' => '<EMAIL>',
            'service_duration' => '2:30'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['bookingDTO']['bookingHubDurationMinutes'])->toBe(150);
    });

    test('handles service duration in plain minutes', function () {
        $webhookData = [
            'id' => 'duration-test',
            'email' => '<EMAIL>',
            'service_duration' => '90'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['bookingDTO']['bookingHubDurationMinutes'])->toBe(90);
    });

    test('handles invalid service duration gracefully', function () {
        $webhookData = [
            'id' => 'duration-test',
            'email' => '<EMAIL>',
            'service_duration' => 'invalid'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['bookingDTO']['bookingHubDurationMinutes'])->toBe(60);
    });

    test('handles empty service duration gracefully', function () {
        $webhookData = [
            'id' => 'duration-test',
            'email' => '<EMAIL>',
            'service_duration' => ''
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result['bookingDTO']['bookingHubDurationMinutes'])->toBe(60);
    });
});
