<?php

use App\Services\FreshcarApi\FreshcarApiRepository;
use App\Services\FreshcarApi\IFreshCarApiClient;
use Illuminate\Support\Facades\Log;

beforeEach(function () {
    $this->mockClient = \Mockery::mock(IFreshCarApiClient::class);
    $this->repository = new FreshcarApiRepository($this->mockClient);
});

describe('emailAvailable', function () {
    test('returns true when client returns true', function () {
        $this->mockClient->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andReturn(true);

        $result = $this->repository->emailAvailable('<EMAIL>');

        expect($result)->toBeTrue();
    });

    test('returns false when client returns false', function () {
        $this->mockClient->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andReturn(false);

        $result = $this->repository->emailAvailable('<EMAIL>');

        expect($result)->toBeFalse();
    });

    test('returns null and logs error when client throws exception', function () {
        Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        Log::shouldReceive('error')->once();

        $this->mockClient->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andThrow(new Exception('API Error'));

        $result = $this->repository->emailAvailable('<EMAIL>');

        expect($result)->toBeNull();
    });
});

describe('generateTemporaryPassword', function () {
    test('generates password with correct format', function () {
        $password = $this->repository->generateTemporaryPassword();

        expect($password)
            ->toStartWith('Temp')
            ->toEndWith('!')
            ->toHaveLength(9);

        // Check that it contains 4 digits between 'Temp' and '!'
        expect(preg_match('/^Temp\d{4}!$/', $password))->toBe(1);
    });

    test('generates different passwords on multiple calls', function () {
        $password1 = $this->repository->generateTemporaryPassword();
        $password2 = $this->repository->generateTemporaryPassword();

        expect($password1)->not->toBe($password2);
    });
});

describe('transformGoHighLevelData', function () {
    test('transforms webhook data correctly', function () {
        $webhookData = [
            'id' => 'VOn9DcpHfTGo5iGwYHdH',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'tags' => 'urgent repair',
            'opportunity_name' => 'Car Repair Booking',
            'lead_value' => 150,
            'owner' => 'Sales Rep',
            'country' => 'GB',
            'location' => [
                'fullAddress' => '123 Main St, London'
            ]
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result)
            ->toHaveKey('firstName', 'John')
            ->toHaveKey('lastName', 'Doe')
            ->toHaveKey('email', '<EMAIL>')
            ->toHaveKey('additionalComments', 'urgent repair')
            ->toHaveKey('notes', 'Booking created from GoHighLevel opportunity: Car Repair Booking')
            ->toHaveKey('totalCost', 150)
            ->toHaveKey('resourceName', 'Sales Rep')
            ->toHaveKey('fullAddress', '123 Main St, London')
            ->toHaveKey('country', 'GB')
            ->toHaveKey('bookingReferenceNumber')
            ->toHaveKey('requestedDate')
            ->toHaveKey('confirmedDate')
            ->toHaveKey('preferredTime');

        // Check booking reference format
        expect($result['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);
    });

    test('handles missing optional fields gracefully', function () {
        $webhookData = [
            'id' => 'test-id',
            'email' => '<EMAIL>'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        dd($result);

        expect($result)
            ->toHaveKey('firstName', '')
            ->toHaveKey('lastName', '')
            ->toHaveKey('email', '<EMAIL>')
            ->toHaveKey('additionalComments', '')
            ->toHaveKey('notes', 'Booking created from GoHighLevel opportunity: Unknown')
            ->toHaveKey('totalCost', 0)
            ->toHaveKey('resourceName', '')
            ->toHaveKey('fullAddress', '')
            ->toHaveKey('country', 'GB');
    });
});

describe('newCustomerBooking', function () {
    test('successfully creates new customer booking', function () {
        $webhookData = [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>'
        ];

        $expectedApiResponse = ['bookingId' => 123];

        // Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        // Log::shouldReceive('info')->once();

        $this->mockClient->shouldReceive('newCustomerBooking')
            ->once()
            ->with(\Mockery::on(function ($data) {
                return $data['registerDTO']['firstName'] === 'Jane'
                    && $data['registerDTO']['lastName'] === 'Smith'
                    && $data['registerDTO']['email'] === '<EMAIL>'
                    && isset($data['registerDTO']['password'])
                    && preg_match('/^Temp\d{4}!$/', $data['password']);
            }))
            ->andReturn($expectedApiResponse);

        $result = $this->repository->newCustomerBooking($webhookData);

        expect($result)->toBe($expectedApiResponse);
    })->skip();

    test('logs error and rethrows exception on failure', function () {
        $webhookData = ['email' => '<EMAIL>'];

        // Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->once();

        $this->mockClient->shouldReceive('newCustomerBooking')
            ->andThrow(new Exception('API Error'));

        expect(fn () => $this->repository->newCustomerBooking($webhookData))
            ->toThrow(Exception::class, 'API Error');
    })->skip();
});

describe('previousCustomerBooking', function () {
    test('successfully creates previous customer booking', function () {
        $webhookData = [
            'email' => '<EMAIL>',
            'opportunity_name' => 'Repeat Service'
        ];

        $expectedApiResponse = ['bookingId' => 456];

        Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        Log::shouldReceive('info')->once();

        $this->mockClient->shouldReceive('previousCustomerBooking')
            ->once()
            ->with(\Mockery::on(function ($data) {
                return $data['email'] === '<EMAIL>'
                    && !isset($data['password']); // Should not include password for existing customers
            }))
            ->andReturn($expectedApiResponse);

        $result = $this->repository->previousCustomerBooking($webhookData);

        expect($result)->toBe($expectedApiResponse);
    })->skip();

    test('logs error and rethrows exception on failure', function () {
        $webhookData = ['email' => '<EMAIL>'];

        Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->once();

        $this->mockClient->shouldReceive('previousCustomerBooking')
            ->andThrow(new Exception('API Error'));

        expect(fn () => $this->repository->previousCustomerBooking($webhookData))
            ->toThrow(Exception::class, 'API Error');
    })->skip();
});
