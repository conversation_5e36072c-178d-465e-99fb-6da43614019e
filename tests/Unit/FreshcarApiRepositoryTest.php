<?php

use App\Services\FreshcarApi\FreshcarApiRepository;
use App\Services\FreshcarApi\IFreshCarApiClient;
use Illuminate\Support\Facades\Log;

beforeEach(function () {
    $this->mockClient = \Mockery::mock(IFreshCarApiClient::class);

    // Mock the config for admin credentials
    config(['services.freshcarapi' => [
        'admin_user' => 'test_admin',
        'admin_password' => 'test_password'
    ]]);

    $this->repository = new FreshcarApiRepository($this->mockClient);

    // Set up common expectations for destructor
    $this->mockClient->shouldReceive('logout')->andReturn(true);
});

describe('emailAvailable', function () {
    test('returns true when client returns true', function () {
        $this->mockClient->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andReturn(true);

        $result = $this->repository->emailAvailable('<EMAIL>');

        expect($result)->toBeTrue();
    });

    test('returns false when client returns false', function () {
        $this->mockClient->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andReturn(false);

        $result = $this->repository->emailAvailable('<EMAIL>');

        expect($result)->toBeFalse();
    });

    test('returns null and logs error when client throws exception', function () {
        Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        Log::shouldReceive('error')->once();

        $this->mockClient->shouldReceive('emailAvailable')
            ->with('<EMAIL>')
            ->andThrow(new Exception('API Error'));

        $result = $this->repository->emailAvailable('<EMAIL>');

        expect($result)->toBeNull();
    });
});

describe('generateTemporaryPassword', function () {
    test('generates password with correct format', function () {
        $password = $this->repository->generateTemporaryPassword();

        expect($password)
            ->toStartWith('Temp')
            ->toEndWith('!')
            ->toHaveLength(9);

        // Check that it contains 4 digits between 'Temp' and '!'
        expect(preg_match('/^Temp\d{4}!$/', $password))->toBe(1);
    });

    test('generates different passwords on multiple calls', function () {
        $password1 = $this->repository->generateTemporaryPassword();
        $password2 = $this->repository->generateTemporaryPassword();

        expect($password1)->not->toBe($password2);
    });
});

describe('transformGoHighLevelData', function () {
    test('transforms webhook data correctly', function () {
        // Mock the adminGetAllValeters call
        $this->mockClient->shouldReceive('authenticateAsUser')
            ->with('test_admin', 'test_password')
            ->andReturn(true);

        $this->mockClient->shouldReceive('adminGetAllValeters')
            ->andReturn(['payload' => [
                ['id' => 1, 'name' => 'Sales Rep'],
                ['id' => 2, 'name' => 'Another Rep']
            ]]);

        $webhookData = [
            'id' => 'VOn9DcpHfTGo5iGwYHdH',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'tags' => 'urgent repair',
            'opportunity_name' => 'Car Repair Booking',
            'lead_value' => 150,
            'owner' => 'Sales Rep',
            'country' => 'GB',
            'location' => [
                'fullAddress' => '123 Main St, London'
            ]
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result)
            ->toHaveKey('registerDTO')
            ->toHaveKey('bookingDTO')
            ->toHaveKey('paymentDetails');

        // Check registerDTO structure
        expect($result['registerDTO'])
            ->toHaveKey('firstName', 'John')
            ->toHaveKey('lastName', 'Doe')
            ->toHaveKey('email', '<EMAIL>')
            ->toHaveKey('password')
            ->toHaveKey('confirmPassword')
            ->toHaveKey('address');

        // Check bookingDTO structure
        expect($result['bookingDTO'])
            ->toHaveKey('additionalComments', 'urgent repair')
            ->toHaveKey('notes', 'Booking created from GoHighLevel opportunity: Car Repair Booking')
            ->toHaveKey('totalCost', 0) // Note: 'lead_value' is not mapped to totalCost, 'Quote Price (internal use only)' is
            ->toHaveKey('resourceName', '') // Note: resourceName comes from customData.contactDiaryName, not 'owner'
            ->toHaveKey('bookingReferenceNumber')
            ->toHaveKey('requestedDate')
            ->toHaveKey('confirmedDate')
            ->toHaveKey('preferredTime')
            ->toHaveKey('country');

        // Check booking reference format
        expect($result['bookingDTO']['bookingReferenceNumber'])
            ->toStartWith('GHL-')
            ->toHaveLength(12);
    });

    test('handles missing optional fields gracefully', function () {
        // Mock the adminGetAllValeters call
        $this->mockClient->shouldReceive('authenticateAsUser')
            ->with('test_admin', 'test_password')
            ->andReturn(true);

        $this->mockClient->shouldReceive('adminGetAllValeters')
            ->andReturn(['payload' => []]);

        $webhookData = [
            'id' => 'test-id',
            'email' => '<EMAIL>'
        ];

        $result = $this->repository->transformGoHighLevelData($webhookData);

        expect($result)
            ->toHaveKey('registerDTO')
            ->toHaveKey('bookingDTO')
            ->toHaveKey('paymentDetails');

        // Check registerDTO structure with minimal data
        expect($result['registerDTO'])
            ->toHaveKey('firstName', '')
            ->toHaveKey('lastName', '')
            ->toHaveKey('email', '<EMAIL>')
            ->toHaveKey('password')
            ->toHaveKey('confirmPassword')
            ->toHaveKey('address');

        // Check bookingDTO structure with minimal data
        expect($result['bookingDTO'])
            ->toHaveKey('additionalComments', '')
            ->toHaveKey('notes', 'Booking created from GoHighLevel opportunity: Unknown')
            ->toHaveKey('totalCost', 0)
            ->toHaveKey('resourceName', '')
            ->toHaveKey('bookingReferenceNumber')
            ->toHaveKey('country');
    });
});

describe('newCustomerBooking', function () {
    test('successfully creates new customer booking', function () {
        $webhookData = [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>'
        ];

        $expectedApiResponse = ['bookingId' => 123];

        // Mock the authentication and valeters call that transformGoHighLevelData makes
        $this->mockClient->shouldReceive('authenticateAsUser')
            ->with('test_admin', 'test_password')
            ->andReturn(true);

        $this->mockClient->shouldReceive('adminGetAllValeters')
            ->andReturn(['payload' => [
                ['id' => 1, 'name' => 'Test Valeter']
            ]]);

        // Mock the logging
        Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        Log::shouldReceive('info')->once();

        $this->mockClient->shouldReceive('newCustomerBooking')
            ->once()
            ->with(\Mockery::on(function ($data) {
                return $data['registerDTO']['firstName'] === 'Jane'
                    && $data['registerDTO']['lastName'] === 'Smith'
                    && $data['registerDTO']['email'] === '<EMAIL>'
                    && isset($data['registerDTO']['password'])
                    && preg_match('/^Temp\d{4}!$/', $data['registerDTO']['password']);
            }))
            ->andReturn($expectedApiResponse);

        $result = $this->repository->newCustomerBooking($webhookData);

        expect($result)->toBe($expectedApiResponse);
    });

    test('logs error and rethrows exception on failure', function () {
        $webhookData = ['email' => '<EMAIL>'];

        // Mock the authentication and valeters call that transformGoHighLevelData makes
        $this->mockClient->shouldReceive('authenticateAsUser')
            ->with('test_admin', 'test_password')
            ->andReturn(true);

        $this->mockClient->shouldReceive('adminGetAllValeters')
            ->andReturn(['payload' => []]);

        // Mock the logging
        Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->once();

        $this->mockClient->shouldReceive('newCustomerBooking')
            ->andThrow(new Exception('API Error'));

        expect(fn () => $this->repository->newCustomerBooking($webhookData))
            ->toThrow(Exception::class, 'API Error');
    });
});

describe('previousCustomerBooking', function () {
    test('successfully creates previous customer booking', function () {
        $webhookData = [
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'opportunity_name' => 'Repeat Service',
            'Registration' => 'ABC123',
            'customData' => [
                'address1' => '123 Test Street',
                'city' => 'Test City',
                'postcode' => 'TE1 2ST',
                'country' => 'GB'
            ]
        ];

        $expectedApiResponse = ['bookingId' => 456];

        // Mock authentication
        $this->mockClient->shouldReceive('authenticateAsUser')
            ->with('test_admin', 'test_password')
            ->andReturn(true);

        // Mock adminGetCustomers for getFCCustomer
        $this->mockClient->shouldReceive('adminGetCustomers')
            ->with('Smith', 0)
            ->andReturn([
                'payload' => [
                    'items' => [
                        ['customerId' => 123, 'email' => '<EMAIL>']
                    ]
                ]
            ]);

        // Mock adminGetCustomer
        $this->mockClient->shouldReceive('adminGetCustomer')
            ->with(123)
            ->andReturn([
                'payload' => [
                    'customer' => [
                        'addresses' => [
                            ['addressId' => 1, 'addressLine1' => '123 test street', 'town' => 'test city', 'postcode' => 'te1 2st', 'country' => 0]
                        ],
                        'cars' => [
                            ['customerCarId' => 1, 'registrationNumber' => 'ABC123']
                        ]
                    ]
                ]
            ]);

        // Mock adminGetCustomerCarClub
        $this->mockClient->shouldReceive('adminGetCustomerCarClub')
            ->with(123)
            ->andReturn([
                'payload' => [
                    ['CustomerCarClubPackageId' => 1]
                ]
            ]);

        // Mock adminGetAllValeters for transformGoHighLevelData
        $this->mockClient->shouldReceive('adminGetAllValeters')
            ->andReturn(['payload' => []]);

        // Mock adminCustomerLogin
        $this->mockClient->shouldReceive('adminCustomerLogin')
            ->with(123)
            ->andReturn(['token' => 'customer-token']);

        // Mock customerBooking (not previousCustomerBooking!)
        $this->mockClient->shouldReceive('customerBooking')
            ->once()
            ->andReturn($expectedApiResponse);

        // Mock logging
        Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        Log::shouldReceive('info')->twice(); // Once for transform data, once for booking data
        Log::shouldReceive('error')->zeroOrMoreTimes(); // In case of any errors

        $result = $this->repository->previousCustomerBooking($webhookData);

        expect($result)->toBe($expectedApiResponse);
    });

    test('logs error and rethrows exception on failure', function () {
        $webhookData = [
            'last_name' => 'Error',
            'email' => '<EMAIL>'
        ];

        // Mock authentication
        $this->mockClient->shouldReceive('authenticateAsUser')
            ->with('test_admin', 'test_password')
            ->andReturn(true);

        // Mock adminGetCustomers to throw exception
        $this->mockClient->shouldReceive('adminGetCustomers')
            ->with('Error', 0)
            ->andThrow(new Exception('API Error'));

        // Mock logging
        Log::shouldReceive('channel')->with('freshcar')->andReturnSelf();
        Log::shouldReceive('error')->atLeast()->once();

        expect(fn () => $this->repository->previousCustomerBooking($webhookData))
            ->toThrow(Exception::class, 'API Error');
    });
});
