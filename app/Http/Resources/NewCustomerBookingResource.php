<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NewCustomerBookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'userDetails' => [
                'id' => $this->resource['userDetails']['id'] ?? null,
                'email' => $this->resource['userDetails']['email'] ?? null,
                'firstName' => $this->resource['userDetails']['first_name'] ?? null,
                'lastName' => $this->resource['userDetails']['last_name'] ?? null,
                'emailVerified' => $this->resource['userDetails']['email_verified_at'] !== null,
                'createdAt' => $this->resource['userDetails']['created_at'] ?? null,
            ],
            'booking' => [
                'id' => $this->resource['booking']['id'] ?? null,
                'bookingReferenceNumber' => $this->resource['booking']['booking_reference_number'] ?? null,
                'requestedDate' => $this->resource['booking']['requested_date'] ?? null,
                'resourceId' => $this->resource['booking']['resource_id'] ?? null,
                'totalCost' => $this->resource['booking']['total_cost'] ?? null,
                'isPrepaid' => $this->resource['booking']['is_prepaid'] ?? false,
                'status' => $this->resource['booking']['status'] ?? null,
            ],
            'paymentDetails' => $this->when(
                isset($this->resource['paymentDetails']),
                [
                    'success' => $this->resource['paymentDetails']['success'] ?? false,
                    'paymentIntentId' => $this->resource['paymentDetails']['payment_intent_id'] ?? null,
                    'message' => $this->resource['paymentDetails']['message'] ?? null,
                ]
            ),
        ];
    }
}
