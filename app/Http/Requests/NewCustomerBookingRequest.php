<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NewCustomerBookingRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Allow anonymous access like the C# controller
    }

    public function rules(): array
    {
        return [
            // RegisterDTO validation rules
            'registerDTO' => 'required|array',
            'registerDTO.title' => 'nullable|string|max:10',
            'registerDTO.firstName' => 'required|string|max:100',
            'registerDTO.lastName' => 'required|string|max:100',
            'registerDTO.contactNumber' => 'nullable|string|max:20',
            'registerDTO.email' => 'required|email|max:255|unique:users,email',
            'registerDTO.dateOfBirth' => 'nullable|date',
            'registerDTO.password' => 'required|string|min:8|confirmed',
            'registerDTO.password_confirmation' => 'required|string',
            
            // Address validation (nested in registerDTO)
            'registerDTO.address' => 'required|array',
            'registerDTO.address.addressLine1' => 'required|string|max:255',
            'registerDTO.address.addressLine2' => 'nullable|string|max:255',
            'registerDTO.address.addressLine3' => 'nullable|string|max:255',
            'registerDTO.address.town' => 'required|string|max:100',
            'registerDTO.address.postcode' => 'required|string|max:20',
            'registerDTO.address.country' => 'nullable|integer',

            // BookingDTO validation rules (NewCustomerCarBookingDTO extends BookingDTO)
            'bookingDTO' => 'required|array',
            'bookingDTO.requestedDate' => 'required|date|after:now',
            'bookingDTO.confirmedDate' => 'nullable|date',
            'bookingDTO.addressId' => 'required|integer',
            'bookingDTO.additionalComments' => 'nullable|string|max:1000',
            'bookingDTO.notes' => 'nullable|string|max:1000',
            'bookingDTO.preferredTime' => 'nullable|date',
            'bookingDTO.bookingStatusId' => 'required|integer',
            'bookingDTO.totalCost' => 'required|numeric|min:0',
            'bookingDTO.enquiryStatus' => 'nullable|string|max:50',
            'bookingDTO.resourceId' => 'required|integer',
            'bookingDTO.resourceName' => 'nullable|string|max:255',
            'bookingDTO.timeOfDay' => 'nullable|string|max:50',
            'bookingDTO.bookingReferenceNumber' => 'nullable|string|max:100',
            'bookingDTO.bookingHubDurationMinutes' => 'nullable|string|max:10',
            'bookingDTO.overridePrice' => 'nullable|numeric|min:0',
            'bookingDTO.isPrepaid' => 'boolean',
            'bookingDTO.isRefunded' => 'boolean',
            'bookingDTO.customerCarClubPackageId' => 'nullable|integer',
            'bookingDTO.currency' => 'nullable|integer',

            // BookingCustomerCars validation
            'bookingDTO.bookingCustomerCars' => 'required|array|min:1',
            'bookingDTO.bookingCustomerCars.*.customerCarId' => 'required|integer',
            'bookingDTO.bookingCustomerCars.*.packageGroupId' => 'required|integer',
            'bookingDTO.bookingCustomerCars.*.registrationNumber' => 'required|string|max:20',
            'bookingDTO.bookingCustomerCars.*.makeAndModel' => 'required|string|max:255',
            'bookingDTO.bookingCustomerCars.*.category' => 'nullable|string|max:50',
            'bookingDTO.bookingCustomerCars.*.colour' => 'nullable|string|max:50',
            'bookingDTO.bookingCustomerCars.*.year' => 'nullable|string|max:10',
            'bookingDTO.bookingCustomerCars.*.optionalExtras' => 'nullable|array',
            'bookingDTO.bookingCustomerCars.*.optionalExtras.*.id' => 'required|integer',
            'bookingDTO.bookingCustomerCars.*.optionalExtras.*.name' => 'required|string|max:255',
            'bookingDTO.bookingCustomerCars.*.optionalExtras.*.price' => 'required|numeric|min:0',

            // PaymentDetails validation rules
            'paymentDetails' => 'nullable|array',
            'paymentDetails.isPayNow' => 'boolean',
            'paymentDetails.paymentMethodId' => 'nullable|string|max:255',
            'paymentDetails.paymentIntentId' => 'nullable|string|max:255',
            'paymentDetails.paymentIntentClientSecret' => 'nullable|string|max:500',
            'paymentDetails.requiresAction' => 'boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'registerDTO.required' => 'Registration details are required.',
            'registerDTO.firstName.required' => 'First name is required.',
            'registerDTO.lastName.required' => 'Last name is required.',
            'registerDTO.email.required' => 'Email address is required.',
            'registerDTO.email.email' => 'Please provide a valid email address.',
            'registerDTO.email.unique' => 'This email address is already registered.',
            'registerDTO.password.required' => 'Password is required.',
            'registerDTO.password.min' => 'Password must be at least 8 characters long.',
            'registerDTO.password.confirmed' => 'Password confirmation does not match.',
            
            'registerDTO.address.required' => 'Address details are required.',
            'registerDTO.address.addressLine1.required' => 'Address line 1 is required.',
            'registerDTO.address.town.required' => 'Town is required.',
            'registerDTO.address.postcode.required' => 'Postcode is required.',

            'bookingDTO.required' => 'Booking details are required.',
            'bookingDTO.requestedDate.required' => 'Requested booking date is required.',
            'bookingDTO.requestedDate.after' => 'Booking date must be in the future.',
            'bookingDTO.addressId.required' => 'Address ID is required.',
            'bookingDTO.bookingStatusId.required' => 'Booking status is required.',
            'bookingDTO.totalCost.required' => 'Total cost is required.',
            'bookingDTO.resourceId.required' => 'Resource ID is required.',

            'bookingDTO.bookingCustomerCars.required' => 'At least one car must be selected for booking.',
            'bookingDTO.bookingCustomerCars.min' => 'At least one car must be selected for booking.',
            'bookingDTO.bookingCustomerCars.*.registrationNumber.required' => 'Car registration number is required.',
            'bookingDTO.bookingCustomerCars.*.makeAndModel.required' => 'Car make and model is required.',
            'bookingDTO.bookingCustomerCars.*.packageGroupId.required' => 'Package selection is required for each car.',
        ];
    }

    public function attributes(): array
    {
        return [
            'registerDTO.firstName' => 'first name',
            'registerDTO.lastName' => 'last name',
            'registerDTO.email' => 'email address',
            'registerDTO.contactNumber' => 'contact number',
            'registerDTO.address.addressLine1' => 'address line 1',
            'registerDTO.address.town' => 'town',
            'registerDTO.address.postcode' => 'postcode',
            'bookingDTO.requestedDate' => 'requested date',
            'bookingDTO.totalCost' => 'total cost',
            'bookingDTO.resourceId' => 'resource',
        ];
    }
}
