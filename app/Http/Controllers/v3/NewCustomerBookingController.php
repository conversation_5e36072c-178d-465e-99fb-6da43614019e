<?php

namespace App\Http\Controllers\v3;

use App\Http\Controllers\Controller;
use App\Http\Requests\NewCustomerBookingRequest;
use App\Http\Resources\NewCustomerBookingResource;
use App\Services\Contracts\NewCustomerBookingServiceInterface;
use App\Traits\HttpResponse;
use Illuminate\Http\JsonResponse;
use Exception;

class NewCustomerBookingController extends Controller
{
    use HttpResponse;

    public function __construct(
        private readonly NewCustomerBookingServiceInterface $newCustomerBookingService
    ) {
        // No authentication middleware - this endpoint allows anonymous access like the C# controller
    }

    /**
     * Create a new customer booking with user registration
     * 
     * This endpoint handles the complete flow of:
     * 1. User registration
     * 2. Payment processing (if required)
     * 3. Booking creation
     * 
     * @param NewCustomerBookingRequest $request
     * @return JsonResponse
     */
    public function store(NewCustomerBookingRequest $request): JsonResponse
    {
        try {
            $validatedData = $request->validated();
            
            $result = $this->newCustomerBookingService->createNewCustomerBooking($validatedData);

            if ($result['success']) {
                return $this->resourceResponse(
                    new NewCustomerBookingResource($result),
                    $result['message'],
                    $result['status_code']
                );
            }

            return $this->response(
                null,
                $result['message'],
                $result['status_code']
            );
        } catch (Exception $e) {
            return $this->response(
                null,
                'An unexpected error occurred while processing your booking. Please try again.',
                500
            );
        }
    }
}
