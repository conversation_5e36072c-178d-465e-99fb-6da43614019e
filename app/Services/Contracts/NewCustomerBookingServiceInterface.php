<?php

namespace App\Services\Contracts;

use App\Models\User;
use Illuminate\Http\JsonResponse;

interface NewCustomerBookingServiceInterface
{
    /**
     * Create a new customer booking with user registration
     *
     * @param array $data The validated request data containing registerDTO, bookingDTO, and paymentDetails
     * @return array Returns an array with success status, data, message, and HTTP status code
     */
    public function createNewCustomerBooking(array $data): array;
}
