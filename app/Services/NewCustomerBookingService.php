<?php

namespace App\Services;

use App\Models\User;
use App\Services\Contracts\NewCustomerBookingServiceInterface;
use App\Services\UserService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class NewCustomerBookingService implements NewCustomerBookingServiceInterface
{
    public function __construct(
        private readonly UserService $userService
    ) {}

    /**
     * Create a new customer booking with user registration
     *
     * @param array $data The validated request data containing registerDTO, bookingDTO, and paymentDetails
     * @return array Returns an array with success status, data, message, and HTTP status code
     */
    public function createNewCustomerBooking(array $data): array
    {
        try {
            return DB::transaction(function () use ($data) {
                // Step 1: Register the new user
                $userRegistrationResult = $this->registerNewUser($data['registerDTO']);
                
                if (!$userRegistrationResult['success']) {
                    return $userRegistrationResult;
                }

                $user = $userRegistrationResult['user'];

                // Step 2: Handle payment if required
                $paymentResult = null;
                if (isset($data['paymentDetails']) && $data['paymentDetails']['isPayNow']) {
                    $paymentResult = $this->processPayment($data['paymentDetails'], $user);
                    
                    if (!$paymentResult['success']) {
                        Log::warning('Payment processing failed for new customer booking', [
                            'user_id' => $user->id,
                            'email' => $user->email,
                            'error' => $paymentResult['message']
                        ]);
                        // Continue with booking creation even if payment fails
                        // The C# code shows this behavior
                    }
                }

                // Step 3: Create the booking
                $bookingResult = $this->createBooking($data['bookingDTO'], $user, $paymentResult);
                
                if (!$bookingResult['success']) {
                    if ($paymentResult && $paymentResult['success']) {
                        Log::warning('Payment was successful but booking creation failed', [
                            'user_id' => $user->id,
                            'payment_intent_id' => $paymentResult['payment_intent_id'] ?? null
                        ]);
                        
                        // TODO: Send notification about successful payment with failed booking
                        // This would require implementing email notification service
                    }
                    
                    return $bookingResult;
                }

                // Step 4: Update booking with payment information if payment was successful
                if ($paymentResult && $paymentResult['success'] && isset($paymentResult['payment_intent_id'])) {
                    $this->updateBookingPaymentInformation(
                        $bookingResult['booking']['id'], 
                        $paymentResult['payment_intent_id']
                    );
                }

                return [
                    'success' => true,
                    'data' => [
                        'userDetails' => $user->toArray(),
                        'booking' => $bookingResult['booking'],
                        'paymentDetails' => $paymentResult
                    ],
                    'message' => 'New customer booking created successfully',
                    'status_code' => 201
                ];
            });
        } catch (Exception $e) {
            Log::error('Failed to create new customer booking', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'success' => false,
                'data' => null,
                'message' => 'An error occurred while creating the booking. Please try again.',
                'status_code' => 500
            ];
        }
    }

    /**
     * Register a new user
     */
    private function registerNewUser(array $registerData): array
    {
        try {
            // Map the registerDTO structure to Laravel user structure
            $userData = [
                'first_name' => $registerData['firstName'],
                'last_name' => $registerData['lastName'],
                'email' => $registerData['email'],
                'password' => $registerData['password'],
                'birth_date' => $registerData['dateOfBirth'] ?? null,
            ];

            $user = $this->userService->create($userData);
            
            // TODO: Create address record
            // This would require implementing an Address model and service
            if (isset($registerData['address'])) {
                $this->createUserAddress($user, $registerData['address']);
            }

            return [
                'success' => true,
                'user' => $user,
                'message' => 'User registered successfully'
            ];
        } catch (Exception $e) {
            Log::error('Failed to register new user', [
                'error' => $e->getMessage(),
                'email' => $registerData['email'] ?? 'unknown'
            ]);

            return [
                'success' => false,
                'user' => null,
                'message' => 'Failed to register user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process payment for the booking
     */
    private function processPayment(array $paymentDetails, User $user): array
    {
        // TODO: Implement payment processing with Stripe
        // This would integrate with the existing Stripe/payment service
        
        Log::info('Payment processing requested', [
            'user_id' => $user->id,
            'is_pay_now' => $paymentDetails['isPayNow'],
            'payment_method_id' => $paymentDetails['paymentMethodId'] ?? null
        ]);

        // Placeholder implementation
        return [
            'success' => true,
            'payment_intent_id' => $paymentDetails['paymentIntentId'] ?? null,
            'message' => 'Payment processed successfully'
        ];
    }

    /**
     * Create a booking for the user
     */
    private function createBooking(array $bookingData, User $user, ?array $paymentResult): array
    {
        // TODO: Implement booking creation
        // This would require implementing Booking model and related services
        
        Log::info('Creating booking', [
            'user_id' => $user->id,
            'requested_date' => $bookingData['requestedDate'],
            'resource_id' => $bookingData['resourceId'],
            'is_prepaid' => $paymentResult && $paymentResult['success']
        ]);

        // Placeholder implementation
        $booking = [
            'id' => rand(1000, 9999), // Temporary ID
            'user_id' => $user->id,
            'requested_date' => $bookingData['requestedDate'],
            'resource_id' => $bookingData['resourceId'],
            'total_cost' => $bookingData['totalCost'],
            'is_prepaid' => $paymentResult && $paymentResult['success'],
            'booking_reference_number' => 'BK' . time(),
            'status' => 'confirmed'
        ];

        return [
            'success' => true,
            'booking' => $booking,
            'message' => 'Booking created successfully'
        ];
    }

    /**
     * Create address for the user
     */
    private function createUserAddress(User $user, array $addressData): void
    {
        // TODO: Implement address creation
        // This would require implementing an Address model
        
        Log::info('Creating user address', [
            'user_id' => $user->id,
            'address_line_1' => $addressData['addressLine1']
        ]);
    }

    /**
     * Update booking with payment information
     */
    private function updateBookingPaymentInformation(int $bookingId, string $paymentIntentId): void
    {
        // TODO: Implement booking payment information update
        
        Log::info('Updating booking payment information', [
            'booking_id' => $bookingId,
            'payment_intent_id' => $paymentIntentId
        ]);
    }
}
