<?php

namespace App\Services\GoHighLevel\WebhookHandlers;

use App\Services\FreshcarApi\IFreshcarApiRepository;
use App\Services\WebhookActivityService;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookClient\Jobs\ProcessWebhookJob;
use Throwable;

class OpportunityStatusUpdateHandler extends ProcessWebhookJob
{
    public function handle()
    {
        $freshcarApiRepository = resolve(IFreshcarApiRepository::class);
        $webhookActivityService = resolve(WebhookActivityService::class);
        $startTime = microtime(true);

        try {
            $webhookActivityService->logWebhookReceived($this->webhookCall);
            $webhookActivityService->logProcessingStarted($this->webhookCall);

            $payload = $this->webhookCall->payload ?? [];

            if (is_string($payload)) {
                $payload = json_decode($payload, true) ?? [];
            }

            Log::channel('webhook')->info('webhook:gohighlevel:opportunity-status-handler:received-payload:', ['payload' => $payload]);

            $webhookData = is_array($payload) && isset($payload[0]) ? $payload[0] : $payload;
            $customData = isset($payload['customData']) &&  is_array($payload['customData'])
                ? $payload['customData'] : [];

            // Check if status is 'won' - only process won opportunities
            $status = $webhookData['status'] ?? 'unknown';
            if ($status !== 'won') {
                Log::channel('webhook')->info('webhook:gohighlevel:opportunity-status-handler:skipping - status not won', [
                    'status' => $status
                ]);
                return;
            }

            $customerEmail = $webhookData['email'] ?? null;
            if (!$customerEmail) {
                Log::channel('webhook')->error('webhook:gohighlevel:opportunity-status-handler:missing-email', $webhookData);
                return;
            }

            Log::channel('webhook')->info('webhook:gohighlevel:opportunity-status-handler:processing-won-opportunity', [
                'email' => $customerEmail,
            ]);

            $isEmailAvailable = $freshcarApiRepository->emailAvailable($customerEmail);

            // Handle email availability check failure
            if ($isEmailAvailable === null) {
                Log::channel('webhook')->error('webhook:gohighlevel:opportunity-status-handler:email-check-failed', [
                    'email' => $customerEmail
                ]);
                return;
            }

            $webhookActivityService->logEmailCheck(
                $this->webhookCall,
                $customerEmail,
                $isEmailAvailable
            );

            if ($isEmailAvailable) {
                // New customer
                $webhookActivityService->log('webhook:gohighlevel:opportunity-status-handler:creating-new-customer-booking', [
                    'email' => $customerEmail
                ]);
                $result = $freshcarApiRepository->newCustomerBooking($webhookData);
                $bookingType = 'new_customer';
            } else {
                $webhookActivityService->log('webhook:gohighlevel:opportunity-status-handler:creating-previous-customer-booking', [
                    'email' => $customerEmail
                ]);
                $result = $freshcarApiRepository->previousCustomerBooking($webhookData);
                $bookingType = 'existing_customer';
            }

            $webhookActivityService->logBookingCreated(
                $this->webhookCall,
                $customerEmail,
                $bookingType,
                $result,
                ['opportunity_name' => $webhookData['opportunity_name'] ?? 'unknown']
            );

            Log::channel('webhook')->info('webhook:gohighlevel:opportunity-status-handler:booking-created-successfully', [
                'email' => $customerEmail,
                'result' => $result
            ]);

            $processingTime = microtime(true) - $startTime;
            $webhookActivityService->logProcessingCompleted(
                $this->webhookCall,
                $processingTime,
                [
                    'customer_email' => $customerEmail,
                    'booking_type' => $bookingType,
                    'opportunity_name' => $webhookData['opportunity_name'] ?? 'unknown'
                ]
            );
        } catch (Throwable $t) {
            Log::channel('webhook')->error('webhook:gohighlevel:opportunity-status-handler:error: ' . $t->getMessage(), [
                'trace' => $t->getTraceAsString()
            ]);

            $webhookActivityService->logProcessingFailed(
                $this->webhookCall,
                $t->getMessage(),
                [
                    'exception_class' => get_class($t),
                    'file' => $t->getFile(),
                    'line' => $t->getLine(),
                ]
            );
        }
    }
}
