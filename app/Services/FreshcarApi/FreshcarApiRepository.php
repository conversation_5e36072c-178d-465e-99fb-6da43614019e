<?php

namespace App\Services\FreshcarApi;

use App\Services\FreshcarApi\Enums\Country;
use App\Services\FreshcarApi\Enums\Currency;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;
use DateTime;
use DateTimeZone;

class FreshcarApiRepository implements IFreshcarApiRepository
{
    private array $config;

    public function __construct(
        public IFreshCarApiClient $iFreshCarApiClient
    ) {
        $this->config = config('services.freshcarapi');
    }

    public function __destruct()
    {
        $this->iFreshCarApiClient->logout();
    }
    /**
     * Check if the email is available for new registration
     *
     * @return bool|null
     */
    public function emailAvailable(string $email): ?bool
    {
        try {
            return $this->iFreshCarApiClient->emailAvailable($email);
        } catch (Throwable $t) {
            Log::channel('freshcar')->error('Error checking email availability: ' . $t->getMessage(), [
                'email' => $email,
                'trace' => $t->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Handle new customer booking
     *
     * @param array $webhookData
     * @return array|null
     */
    public function newCustomerBooking(array $webhookData): ?array
    {
        try {
            $transformedData = $this->transformGoHighLevelData($webhookData);
            // $transformedData['password'] = $this->generateTemporaryPassword();

            Log::channel('freshcar')->info(
                'freshcar:new-customer-booking:transformed-data',
                $transformedData
            );

            $response = $this->iFreshCarApiClient->newCustomerBooking($transformedData);
            return $response;
        } catch (Throwable $t) {
            Log::error('freshcar:new-customer-booking:error', [
                'webhookData' => $webhookData,
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    private function authenticateAsAdmin(): bool
    {
        if (
            !$this->iFreshCarApiClient->authenticateAsUser(
                $this->config['admin_user'],
                $this->config['admin_password']
            )
        ) {
            Log::channel('freshcar')->error('freshcar:admin-get-all-valeters:authenticate-failed');
            return false;
        }
        return true;
    }

    public function getFCCustomer(string $lastName, string $email): ?array
    {
        try {
            $this->authenticateAsAdmin();
            $currentPage = 0;
            while ($response = $this->iFreshCarApiClient->adminGetCustomers($lastName, $currentPage++)) {
                if (!isset($response['payload']['items']) || !is_array($response['payload']['items']) || !count($response['payload']['items'])) {
                    return null;
                }

                foreach ($response['payload']['items'] as $item) {
                    if (trim($item['email']) == trim($email)) {
                        return $item;
                    }
                }
            }
        } catch (Throwable $t) {
            Log::channel('freshcar')->error('freshcar-rep:getfccustomer:error', [
                'msg' => $t->getMessage(),
                'lastName' => $lastName,
                'email' => $email,
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    public function getFCAddressId(array $fcAddresses, array $webhookData): bool | int
    {
        $needle = $this->transformGHLToAddressDTO($webhookData);
        foreach ($fcAddresses as $fcAddress) {
            if (
                strtolower(trim($fcAddress['addressLine1'])) == strtolower(trim($needle['addressLine1']))
                && strtolower(trim($fcAddress['town'])) == strtolower(trim($needle['town']))
                && strtolower(trim($fcAddress['postcode'])) == strtolower(trim($needle['postcode']))
                && strtolower(trim($fcAddress['country'])) === strtolower(trim($needle['country']))
            ) {
                return (int) $fcAddress['addressId'];
            }
        }

        return false;
    }

    public function createFCAddress(int $customerId, array $webhookData)
    {
        $addressDTO = $this->transformGHLToAddressDTO($webhookData);
        $response = $this->iFreshCarApiClient->adminAddCustomerAddresses($customerId, $addressDTO);
        if (!isset($response['payload']) || !is_array($response['payload'])) {
            return null;
        }

        return $response['payload'];
    }

    public function getFCCustomerCarId(array $fcCars, string $regNumber): bool | int
    {
        foreach ($fcCars as $fcCar) {
            if (strtolower(trim($fcCar['registrationNumber'])) == strtolower(trim($regNumber))) {
                return (int) $fcCar['customerCarId'];
            }
        }
        return false;
    }

    public function createFCCar(int $customerId, array $webhookData): ?array
    {
        $carDTO = $this->tranformGHLToCarDTO($webhookData);
        $response = $this->iFreshCarApiClient->adminAddCustomerCars($customerId, $carDTO);
        if (!isset($response['payload']) || !is_array($response['payload'])) {
            return null;
        }

        return $response['payload'];
    }

    public function getFCCustomerCarClubPackageId(array $fcCustomerCarClub)
    {
        $first = array_shift($fcCustomerCarClub);

        return $first['CustomerCarClubPackageId'] ?? null;
    }
    /**
     * Handle previous customer booking
     *
     * @param array $webhookData
     * @return array|null
     */
    public function previousCustomerBooking(array $webhookData): ?array
    {
        try {
            $this->authenticateAsAdmin();
            $fcCustomerBasic = $this->getFCCustomer($webhookData['last_name'], $webhookData['email']);
            if (!$fcCustomerBasic) {
                throw new Exception(sprintf("customer with %s %s not found", $webhookData['last_name'], $webhookData['email']));
            }
            $customerId = $fcCustomerBasic['customerId'] ? (int) $fcCustomerBasic['customerId'] : null;
            if (!$customerId) {
                throw new Exception(sprintf("customerID not found"));
            }

            $fcCustomerFull = $this->iFreshCarApiClient->adminGetCustomer($customerId);
            if (!$fcCustomerFull) {
                throw new Exception(sprintf("Unable get details for customerId %d", $customerId));
            }

            $fcCustomerCarClub = $this->iFreshCarApiClient->adminGetCustomerCarClub($customerId);
            $fcAddressId = $this->getFCAddressId($fcCustomerFull['payload']['customer']['addresses'], $webhookData);
            $fcCustomerCarId = $this->getFCCustomerCarId($fcCustomerFull['payload']['customer']['cars'], $webhookData['Registration']);
            $fcCustomerCarClubPackageId = $this->getFCCustomerCarClubPackageId($fcCustomerCarClub['payload']);

            if (!$fcAddressId) {
                $fcAddress = $this->createFCAddress($customerId, $webhookData);
                $fcAddressId = $fcAddress['addressId'];
            }

            if (!$fcCustomerCarId) {
                $fcCar = $this->createFCCar($customerId, $webhookData);
                $fcCustomerCarId = $fcCar['customerCarId'];
            }

            $transformedData = $this->transformGoHighLevelData($webhookData);
            $bookingDTO = $transformedData['bookingDTO'];
            $bookingDTO['addressId'] = $fcAddressId;
            $bookingDTO['bookingCustomerCars'][0]['customerCarId'] = $fcCustomerCarId;
            $bookingDTO['customerCarClubPackageId'] = $fcCustomerCarClubPackageId;

            Log::channel('freshcar')->info('freshcar:previous-customer-booking:transformed-data', [
                'email' => $webhookData['email'] ?? 'unknown',
                'bookingDTO' => $bookingDTO
            ]);
            $this->iFreshCarApiClient->adminCustomerLogin($customerId);
            return $this->iFreshCarApiClient->customerBooking($bookingDTO);
        } catch (Throwable $t) {
            Log::channel('freshcar')->error('freshcar:previous-customer-booking:error', [
                'msg' => $t->getMessage(),
                'webhookData' => $webhookData,
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }

    /**
     * Map country code to Country enum value
     *
     * @param string|null $countryCode
     * @return int
     */
    private function mapCountry(?string $countryCode): int
    {
        return match (strtoupper($countryCode ?? 'GB')) {
            'IE' => Country::IE->value,
            default => Country::GB->value,
        };
    }

    /**
     * Map country to Currency enum value
     *
     * @param int $country Country enum value
     * @return int
     */
    private function mapCurrencyByCountry(int $country): int
    {
        return match ($country) {
            Country::IE->value => Currency::EUR->value,
            default => Currency::GBP->value,
        };
    }

    /**
     * Convert duration from H:i format to minutes
     *
     * @param string $duration Duration in H:i format (e.g. "1:30")
     * @return int Total minutes
     */
    private function convertDurationToMinutes(string $duration): int
    {
        // Default to 60 minutes if format is invalid
        if (!preg_match('/^(\d+):(\d+)$/', $duration, $matches)) {
            return 60;
        }

        $hours = (int) $matches[1];
        $minutes = (int) $matches[2];

        return ($hours * 60) + $minutes;
    }

    private function getValetIdByName(string $valetName, array $valeters): int
    {
        foreach ($valeters as $valet) {
            if ($valet['name'] === $valetName) {
                return $valet['id'];
            }
        }
        return 0;
    }

    public function transformGHLToAddressDTO(array $webhookData): ?array
    {
        $customData = $webhookData['customData'] ?? [];
        $country = $this->mapCountry($webhookData['country'] ?? $customData['country'] ?? null);
        return [
            'addressLine1' => $customData['address1'] ?? '',
            'addressLine2' => '',
            'addressLine3' => '',
            'town' => $customData['city'] ?? '',
            'postcode' => $customData['postcode'] ?? '',
            'country' => $country,
        ];
    }


    public function tranformGHLToCarDTO(array $webhookData): ?array
    {
        return [
            'registrationNumber' => $webhookData['Registration'] ?? '',
            'makeAndModel' => trim(($webhookData['Vehicle Make'] ?? '') . ' ' . ($webhookData['Vehicle Model'] ?? '')),
            'packageGroupId' => 5,
            'category' => substr($webhookData['pipeline_name'] ?? 'SMART Rep', 0, 10),
            'colour' => '',
            'year' => '',
            'optionalExtras' => [
                ['packageItemId' => 6]
            ],
        ];
    }

    /**
     * Transform GoHighLevel webhook data to FreshCar API format
     *
     * @param array $webhookData
     * @return array
     */
    public function transformGoHighLevelData(array $webhookData): array
    {
        // Extract basic customer information
        $firstName = $webhookData['first_name'] ?? '';
        $lastName = $webhookData['last_name'] ?? '';
        $email = $webhookData['email'] ?? '';
        $fullAddress = $webhookData['location']['fullAddress'] ?? '';
        $customData = $webhookData['customData'] ?? [];
        $resourceName = $customData['contactDiaryName'] ?? '';
        $valeters = $this->adminGetAllValeters();
        $resourceId = 0;

        if ($resourceName && $valeters) {
            $resourceId = $this->getValetIdByName($resourceName ?? '', $valeters ?? []);
        }

        $bookingReference = 'GHL-' . strtoupper(substr(md5($webhookData['id'] ?? uniqid()), 0, 8));
        $country = $this->mapCountry($webhookData['country'] ?? $customData['country'] ?? null);
        $currency = $this->mapCurrencyByCountry($country);

        $requestedDate = now()->toISOString();
        try {
            $bookingDate = $webhookData['Booking Date'] ?? now()->format('d/m/Y');
            $bookingTime = $webhookData['Booking Time'] ?? now()->format('H:i');
            $dateTimeString = $bookingDate . ' ' . $bookingTime;
            $dateTime = DateTime::createFromFormat('d/m/Y H:i', $dateTimeString);

            if ($dateTime) {
                $dateTime->setTimezone(new DateTimeZone('UTC'));
                $requestedDate = $dateTime->format('Y-m-d\TH:i:s.v\Z');
            }
        } catch (Throwable $t) {
            // Silently fail and use default now() value
        }

        // Extract car details if available
        $bookingCustomerCars = [];
        if (isset($webhookData['Vehicle Make']) || isset($webhookData['Vehicle Model'])) {
            $bookingCustomerCars[] = $this->tranformGHLToCarDTO($webhookData);
        }
        $password = $this->generateTemporaryPassword();

        return [
            'registerDTO' => [
                'email' => $email,
                'password' => $password,
                'confirmPassword' => $password,
                'title' => '',
                'firstName' => $firstName,
                'lastName' => $lastName,
                'contactNumber' => $webhookData['phone'] ?? '',
                'dateOfBirth' => null,
                'address' => $this->transformGHLToAddressDTO($webhookData),
            ],
            'bookingDTO' => [
                'requestedDate' => $requestedDate,
                'confirmedDate' => $requestedDate,
                'additionalComments' => $webhookData['tags'] ?? '',
                'bookingStatusId' => 2, // it means: confirmed
                'currency' => $currency,
                'bookingCustomerCars' => $bookingCustomerCars,
                'notes' => 'Booking created from GoHighLevel opportunity: ' . ($webhookData['opportunity_name'] ?? 'Unknown'),
                'preferredTime' => $requestedDate,
                'totalCost' => $webhookData['Quote Price (internal use only)'] ?? 0,
                'enquiryStatus' => 'confirmed',
                'resourceId' => $resourceId,
                'resourceName' => $resourceName,
                'bookingReferenceNumber' => $bookingReference,
                'bookingHubDurationMinutes' => '' . $this->convertDurationToMinutes(($webhookData['Service duration'] ?? '') ?: '1:00'),
                'overridePrice' => $webhookData['Quote Price (internal use only)'] ?? 0,
                'country' => $country,
            ],
            'paymentDetails' => [
                'isPayNow' => false,
            ]
        ];
    }

    /**
     * Generate a temporary password for new user registration
     *
     * @return string
     */
    public function generateTemporaryPassword(): string
    {
        return 'Temp' . rand(1000, 9999) . '!';
    }

    public function adminGetAllValeters(): ?array
    {
        try {
            if (
                !$this->iFreshCarApiClient->authenticateAsUser(
                    $this->config['admin_user'],
                    $this->config['admin_password']
                )
            ) {
                Log::channel('freshcar')->error('freshcar:admin-get-all-valeters:authenticate-failed');
                return null;
            }
            $response = $this->iFreshCarApiClient->adminGetAllValeters();
            return $response['payload'] ?? null;
        } catch (Throwable $t) {
            Log::error('freshcar:admin-get-all-valeters:error', [
                'error' => $t->getMessage(),
                'trace' => $t->getTraceAsString()
            ]);
            throw $t;
        }
    }
}
