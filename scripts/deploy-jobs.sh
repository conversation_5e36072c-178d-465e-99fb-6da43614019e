#!/bin/bash

# Cloud Run Jobs Deployment Script
# This script deploys and updates Cloud Run Job configurations

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-$(gcloud config get-value project)}
REGION=${CLOUD_RUN_REGION:-"europe-west2"}
IMAGE_TAG=${IMAGE_TAG:-"latest"}
ENVIRONMENT=${ENVIRONMENT:-"staging"}  # Default to staging for development safety

# Environment-specific configurations
if [ "$ENVIRONMENT" = "staging" ]; then
    CLOUD_SQL_CONNECTION_NAME=${CLOUD_SQL_CONNECTION_NAME:-"fresh-car-test:europe-west2:freshcar-staging-core"}
    DB_DATABASE=${DB_DATABASE:-"postgres"}
    DB_USERNAME=${DB_USERNAME:-"postgres"}
else
    # Production defaults - update these for your production environment
    CLOUD_SQL_CONNECTION_NAME=${CLOUD_SQL_CONNECTION_NAME:-"fresh-car-prod:europe-west2:freshcar-production-core"}
    DB_DATABASE=${DB_DATABASE:-"freshcar_production"}
    DB_USERNAME=${DB_USERNAME:-"freshcar_user"}
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to substitute variables in YAML files
substitute_variables() {
    local yaml_file=$1
    local temp_file="${yaml_file}.tmp"

    # Send log output to stderr to avoid interfering with function return value
    log_info "Processing $yaml_file..." >&2

    # Create a temporary file with substitutions
    # Use a different delimiter for sed to avoid issues with slashes in paths
    sed -e "s|PROJECT_ID|$PROJECT_ID|g" \
        -e "s|CLOUD_SQL_CONNECTION_NAME|$CLOUD_SQL_CONNECTION_NAME|g" \
        -e "s|DB_DATABASE|$DB_DATABASE|g" \
        -e "s|DB_USERNAME|$DB_USERNAME|g" \
        "$yaml_file" > "$temp_file"

    echo "$temp_file"
}

# Function to deploy a single job
deploy_job() {
    local yaml_file=$1
    local job_name=$(basename "$yaml_file" .yaml)

    log_info "Deploying job: $job_name"

    # Substitute variables
    local processed_file=$(substitute_variables "$yaml_file")

    # Deploy the job with Cloud SQL connection
    if gcloud run jobs replace "$processed_file" --region="$REGION" --add-cloudsql-instances="$CLOUD_SQL_CONNECTION_NAME"; then
        log_success "Successfully deployed $job_name"
    else
        log_error "Failed to deploy $job_name"
        rm -f "$processed_file"
        return 1
    fi

    # Clean up temporary file
    rm -f "$processed_file"
}

# Function to deploy all jobs
deploy_all_jobs() {
    log_info "Deploying all Cloud Run Jobs for environment: $ENVIRONMENT"

    local jobs_dir="cloud-run-jobs/$ENVIRONMENT"
    local failed_jobs=()

    if [ ! -d "$jobs_dir" ]; then
        log_error "Jobs directory not found: $jobs_dir"
        log_info "Available environments:"
        ls -la cloud-run-jobs/ | grep "^d" | awk '{print $9}' | grep -v "^\.$\|^\.\.$\|^examples$"
        return 1
    fi

    # Deploy each job configuration
    for yaml_file in "$jobs_dir"/*.yaml; do
        if [ -f "$yaml_file" ]; then
            if ! deploy_job "$yaml_file"; then
                failed_jobs+=("$(basename "$yaml_file")")
            fi
        fi
    done

    # Report results
    if [ ${#failed_jobs[@]} -eq 0 ]; then
        log_success "All jobs deployed successfully!"
    else
        log_error "Failed to deploy the following jobs: ${failed_jobs[*]}"
        return 1
    fi
}

# Function to validate job configurations
validate_jobs() {
    log_info "Validating job configurations..."

    local jobs_dir="cloud-run-jobs/$ENVIRONMENT"
    local validation_errors=()

    if [ ! -d "$jobs_dir" ]; then
        log_error "Jobs directory not found: $jobs_dir"
        log_info "Available environments:"
        ls -la cloud-run-jobs/ | grep "^d" | awk '{print $9}' | grep -v "^\.$\|^\.\.$\|^examples$\|^entrypoints$"
        return 1
    fi

    for yaml_file in "$jobs_dir"/*.yaml; do
        if [ -f "$yaml_file" ]; then
            log_info "Validating $(basename "$yaml_file")..."

            # Basic YAML syntax validation
            if ! python3 -c "import yaml; yaml.safe_load(open('$yaml_file'))" 2>/dev/null; then
                validation_errors+=("$(basename "$yaml_file"): Invalid YAML syntax")
                continue
            fi

            # Check for required fields
            local processed_file=$(substitute_variables "$yaml_file")

            # Validate with gcloud (dry-run)
            if ! gcloud run jobs replace "$processed_file" --region="$REGION" --dry-run >/dev/null 2>&1; then
                validation_errors+=("$(basename "$yaml_file"): Invalid Cloud Run Job configuration")
            fi

            rm -f "$processed_file"
        fi
    done

    if [ ${#validation_errors[@]} -eq 0 ]; then
        log_success "All job configurations are valid!"
    else
        log_error "Validation errors found:"
        for error in "${validation_errors[@]}"; do
            echo "  - $error"
        done
        return 1
    fi
}

# Function to create a new job configuration
create_job_config() {
    local job_name=$1
    local command=$2
    local timeout=${3:-1800}
    local memory=${4:-512Mi}
    local cpu=${5:-1000m}

    if [ -z "$job_name" ] || [ -z "$command" ]; then
        log_error "Job name and command are required"
        echo "Usage: create_job_config <job_name> <command> [timeout] [memory] [cpu]"
        return 1
    fi

    local yaml_file="cloud-run-jobs/${job_name}-job.yaml"

    if [ -f "$yaml_file" ]; then
        log_warning "Job configuration already exists: $yaml_file"
        read -p "Overwrite? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Cancelled"
            return 0
        fi
    fi

    log_info "Creating job configuration: $yaml_file"

    cat > "$yaml_file" << EOF
apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: $job_name
  labels:
    app: freshcar-core
    component: custom-command
spec:
  template:
    spec:
      template:
        spec:
          taskCount: 1
          parallelism: 1
          taskTimeoutSeconds: $timeout
          restartPolicy: Never
          containers:
          - name: $job_name
            image: gcr.io/PROJECT_ID/api:latest
            command: ["/var/www/html/cloud-run-jobs/entrypoints/artisan-command-entrypoint"]
            resources:
              limits:
                cpu: $cpu
                memory: $memory
            env:
            - name: APP_ENV
              value: "production"
            - name: APP_DEBUG
              value: "false"
            - name: LOG_CHANNEL
              value: "stderr"
            - name: DB_CONNECTION
              value: "pgsql"
            - name: DB_HOST
              value: "/cloudsql/CLOUD_SQL_CONNECTION_NAME"
            - name: DB_PORT
              value: "5432"
            - name: DB_DATABASE
              value: "DB_DATABASE"
            - name: DB_USERNAME
              value: "DB_USERNAME"
            - name: CACHE_DRIVER
              value: "database"
            - name: ARTISAN_COMMAND
              value: "$command"
            - name: ARTISAN_ARGS
              value: ""
            - name: APP_KEY
              valueFrom:
                secretKeyRef:
                  name: app-key
                  key: latest
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-password
                  key: latest
tlg-devops@PROJECT_ID.iam.gserviceaccount.com
          volumes:
          - name: cloudsql
            csi:
              driver: gcp-compute-persistent-disk-csi-driver
EOF

    log_success "Job configuration created: $yaml_file"
    log_info "You can now deploy it with: $0 deploy $yaml_file"
}

# Function to update job image
update_job_image() {
    local job_name=$1
    local new_image_tag=${2:-$IMAGE_TAG}

    if [ -z "$job_name" ]; then
        log_error "Job name is required"
        return 1
    fi

    local new_image="gcr.io/$PROJECT_ID/api:$new_image_tag"

    log_info "Updating job $job_name to use image: $new_image"

    if gcloud run jobs update "$job_name" --region="$REGION" --image="$new_image"; then
        log_success "Successfully updated $job_name image"
    else
        log_error "Failed to update $job_name image"
        return 1
    fi
}

# Main script logic
main() {
    case "${1:-help}" in
        "deploy")
            if [ -n "$2" ]; then
                deploy_job "$2"
            else
                deploy_all_jobs
            fi
            ;;
        "validate")
            validate_jobs
            ;;
        "create")
            create_job_config "$2" "$3" "$4" "$5" "$6"
            ;;
        "update-image")
            update_job_image "$2" "$3"
            ;;
        "help"|*)
            echo "Usage: $0 <command> [options]"
            echo ""
            echo "Commands:"
            echo "  deploy [yaml_file]                   - Deploy specific job or all jobs"
            echo "  validate                             - Validate all job configurations"
            echo "  create <name> <command> [timeout] [memory] [cpu] - Create new job config"
            echo "  update-image <job_name> [tag]       - Update job container image"
            echo ""
            echo "Environment Variables:"
            echo "  PROJECT_ID                           - Google Cloud Project ID"
            echo "  REGION                               - Cloud Run region (default: europe-west2)"
            echo "  IMAGE_TAG                            - Container image tag (default: latest)"
            echo "  CLOUD_SQL_CONNECTION_NAME            - Cloud SQL connection name"
            echo "  DB_DATABASE                          - Database name"
            echo "  DB_USERNAME                          - Database username"
            echo ""
            echo "Examples:"
            echo "  $0 deploy"
            echo "  $0 deploy cloud-run-jobs/migrate-job.yaml"
            echo "  $0 validate"
            echo "  $0 create user-cleanup 'user:cleanup --days=30' 3600 1Gi 2000m"
            echo "  $0 update-image laravel-migrate-job v1.2.3"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
