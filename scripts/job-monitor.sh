#!/bin/bash

# Cloud Run Jobs Monitoring Script
# This script provides monitoring and management capabilities for Cloud Run Jobs

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-$(gcloud config get-value project)}
REGION=${CLOUD_RUN_REGION:-"europe-west2"}
ENVIRONMENT=${ENVIRONMENT:-"staging"}  # Default to staging for development safety

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}=== $1 ===${NC}"
}

# Function to get job status
get_job_status() {
    local job_name=$1

    log_header "Job Status: $job_name"

    # Get job details
    gcloud run jobs describe "$job_name" --region="$REGION" --format="table(
        metadata.name:label=NAME,
        spec.template.spec.template.spec.containers[0].image:label=IMAGE,
        status.conditions[0].type:label=STATUS,
        status.conditions[0].lastTransitionTime:label=LAST_UPDATE
    )"

    echo ""

    # Get recent executions
    log_info "Recent executions:"
    gcloud run jobs executions list --job="$job_name" --region="$REGION" --limit=5 --format="table(
        metadata.name:label=EXECUTION,
        status.conditions[0].type:label=STATUS,
        status.startTime:label=START_TIME,
        status.completionTime:label=COMPLETION_TIME,
        spec.taskCount:label=TASKS
    )"
}

# Function to monitor job execution in real-time
monitor_execution() {
    local job_name=$1
    local execution_name=$2
    local follow=${3:-true}

    if [ -z "$execution_name" ]; then
        # Get the latest execution
        execution_name=$(gcloud run jobs executions list --job="$job_name" --region="$REGION" --format="value(metadata.name)" --limit=1)
    fi

    if [ -z "$execution_name" ]; then
        log_error "No executions found for job $job_name"
        return 1
    fi

    log_header "Monitoring Execution: $execution_name"

    # Show execution details
    gcloud run jobs executions describe "$execution_name" --region="$REGION" --format="table(
        metadata.name:label=EXECUTION,
        status.conditions[0].type:label=STATUS,
        status.startTime:label=START_TIME,
        status.completionTime:label=COMPLETION_TIME,
        spec.taskCount:label=TASK_COUNT,
        status.runningCount:label=RUNNING,
        status.succeededCount:label=SUCCEEDED,
        status.failedCount:label=FAILED
    )"

    echo ""

    if [ "$follow" = "true" ]; then
        log_info "Following logs (Ctrl+C to stop)..."

        # Follow logs in real-time
        gcloud logging tail "resource.type=cloud_run_job AND resource.labels.job_name=$job_name AND resource.labels.location=$REGION" --format="value(timestamp,textPayload)"
    else
        # Get recent logs
        log_info "Recent logs:"
        gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=$job_name AND resource.labels.location=$REGION AND timestamp>='-1h'" --format="value(timestamp,textPayload)" --limit=50
    fi
}

# Function to list all jobs
list_all_jobs() {
    log_header "All Cloud Run Jobs"

    gcloud run jobs list --region="$REGION" --format="table(
        metadata.name:label=NAME,
        metadata.labels.component:label=COMPONENT,
        spec.template.spec.template.spec.containers[0].image:label=IMAGE,
        status.conditions[0].type:label=STATUS,
        metadata.creationTimestamp:label=CREATED
    )"
}

# Function to get job metrics
get_job_metrics() {
    local job_name=$1
    local hours=${2:-24}

    log_header "Job Metrics: $job_name (Last $hours hours)"

    # Get execution count
    local execution_count=$(gcloud run jobs executions list --job="$job_name" --region="$REGION" --filter="metadata.creationTimestamp>'-${hours}h'" --format="value(metadata.name)" | wc -l)

    # Get success/failure counts
    local success_count=$(gcloud run jobs executions list --job="$job_name" --region="$REGION" --filter="metadata.creationTimestamp>'-${hours}h' AND status.conditions[0].type=Completed" --format="value(metadata.name)" | wc -l)

    local failed_count=$(gcloud run jobs executions list --job="$job_name" --region="$REGION" --filter="metadata.creationTimestamp>'-${hours}h' AND status.conditions[0].type=Failed" --format="value(metadata.name)" | wc -l)

    echo "Total Executions: $execution_count"
    echo "Successful: $success_count"
    echo "Failed: $failed_count"

    if [ $execution_count -gt 0 ]; then
        local success_rate=$(( success_count * 100 / execution_count ))
        echo "Success Rate: ${success_rate}%"
    fi

    echo ""

    # Show recent executions with status
    log_info "Recent executions:"
    gcloud run jobs executions list --job="$job_name" --region="$REGION" --filter="metadata.creationTimestamp>'-${hours}h'" --limit=10 --format="table(
        metadata.name:label=EXECUTION,
        status.conditions[0].type:label=STATUS,
        status.startTime:label=START_TIME,
        status.completionTime:label=COMPLETION_TIME
    )"
}

# Function to cancel running execution
cancel_execution() {
    local job_name=$1
    local execution_name=$2

    if [ -z "$execution_name" ]; then
        # Get the latest running execution
        execution_name=$(gcloud run jobs executions list --job="$job_name" --region="$REGION" --filter="status.conditions[0].type=Running" --format="value(metadata.name)" --limit=1)
    fi

    if [ -z "$execution_name" ]; then
        log_warning "No running executions found for job $job_name"
        return 1
    fi

    log_warning "Cancelling execution: $execution_name"
    gcloud run jobs executions cancel "$execution_name" --region="$REGION"

    if [ $? -eq 0 ]; then
        log_success "Execution cancelled successfully"
    else
        log_error "Failed to cancel execution"
        return 1
    fi
}

# Function to show job configuration
show_job_config() {
    local job_name=$1

    log_header "Job Configuration: $job_name"

    gcloud run jobs describe "$job_name" --region="$REGION" --format="yaml(
        spec.template.spec.template.spec.containers[0].env,
        spec.template.spec.template.spec.containers[0].resources,
        spec.template.spec.template.spec.taskTimeoutSeconds,
        spec.template.spec.template.spec.restartPolicy
    )"
}

# Function to update job environment variables
update_job_env() {
    local job_name=$1
    local env_vars=$2

    if [ -z "$env_vars" ]; then
        log_error "No environment variables specified"
        return 1
    fi

    log_info "Updating environment variables for job: $job_name"
    log_info "New variables: $env_vars"

    gcloud run jobs update "$job_name" --region="$REGION" --update-env-vars="$env_vars"

    if [ $? -eq 0 ]; then
        log_success "Job updated successfully"
    else
        log_error "Failed to update job"
        return 1
    fi
}

# Main script logic
main() {
    case "${1:-help}" in
        "status")
            get_job_status "$2"
            ;;
        "monitor")
            monitor_execution "$2" "$3" "${4:-true}"
            ;;
        "list")
            list_all_jobs
            ;;
        "metrics")
            get_job_metrics "$2" "${3:-24}"
            ;;
        "cancel")
            cancel_execution "$2" "$3"
            ;;
        "config")
            show_job_config "$2"
            ;;
        "update-env")
            update_job_env "$2" "$3"
            ;;
        "help"|*)
            echo "Usage: $0 <command> [options]"
            echo ""
            echo "Commands:"
            echo "  status <job_name>                    - Show job status and recent executions"
            echo "  monitor <job_name> [execution] [follow] - Monitor job execution (follow=true/false)"
            echo "  list                                 - List all Cloud Run Jobs"
            echo "  metrics <job_name> [hours]          - Show job metrics for specified hours"
            echo "  cancel <job_name> [execution]       - Cancel running execution"
            echo "  config <job_name>                   - Show job configuration"
            echo "  update-env <job_name> <env_vars>    - Update job environment variables"
            echo ""
            echo "Examples:"
            echo "  $0 status laravel-migrate-job"
            echo "  $0 monitor laravel-queue-worker-job"
            echo "  $0 metrics laravel-migrate-job 48"
            echo "  $0 cancel laravel-queue-worker-job"
            echo "  $0 update-env laravel-migrate-job 'MIGRATION_SEED=true,APP_DEBUG=true'"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
