
# Cloud Run Jobs Management Script
# This script provides a convenient interface for managing Laravel Artisan commands via Cloud Run Jobs

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-$(gcloud config get-value project)}
REGION=${CLOUD_RUN_REGION:-"europe-west2"}
IMAGE_TAG=${IMAGE_TAG:-"latest"}
ENVIRONMENT=${ENVIRONMENT:-"staging"}  # Default to staging for development safety

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if gcloud is authenticated
check_auth() {
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "No active gcloud authentication found. Please run 'gcloud auth login'"
        exit 1
    fi
}

# Check if project is set
check_project() {
    if [ -z "$PROJECT_ID" ]; then
        log_error "No Google Cloud project set. Please run 'gcloud config set project YOUR_PROJECT_ID'"
        exit 1
    fi
    log_info "Using project: $PROJECT_ID"
}

# Function to execute a Cloud Run Job
execute_job() {
    local job_name=$1
    local env_vars=$2
    local wait_for_completion=${3:-true}

    # Add environment suffix to job name
    local full_job_name="${job_name}-${ENVIRONMENT}"

    log_info "Executing job: $full_job_name (Environment: $ENVIRONMENT)"

    local execute_cmd="gcloud run jobs execute $full_job_name --region=$REGION"

    if [ -n "$env_vars" ]; then
        execute_cmd="$execute_cmd --update-env-vars=$env_vars"
    fi

    if [ "$wait_for_completion" = "true" ]; then
        execute_cmd="$execute_cmd --wait"
    fi

    eval $execute_cmd

    if [ $? -eq 0 ]; then
        log_success "Job $full_job_name executed successfully"
    else
        log_error "Job $full_job_name failed"
        return 1
    fi
}

# Function to get job logs
get_job_logs() {
    local job_name=$1
    local execution_name=$2

    if [ -z "$execution_name" ]; then
        # Get the latest execution
        execution_name=$(gcloud run jobs executions list --job=$job_name --region=$REGION --format="value(name)" --limit=1)
    fi

    if [ -z "$execution_name" ]; then
        log_error "No executions found for job $job_name"
        return 1
    fi

    log_info "Getting logs for execution: $execution_name"
    gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=$job_name AND resource.labels.location=$REGION" --format="value(textPayload)" --limit=100
}

# Function to list job executions
list_executions() {
    local job_name=$1

    log_info "Listing executions for job: $job_name"
    gcloud run jobs executions list --job=$job_name --region=$REGION
}

# Function to run migrations
run_migrations() {
    local with_seed=${1:-false}
    local env_vars=""

    if [ "$with_seed" = "true" ]; then
        env_vars="MIGRATION_SEED=true"
        log_info "Running migrations with seeders..."
    else
        log_info "Running migrations..."
    fi

    execute_job "laravel-migrate-job" "$env_vars"
}

# Function to run queue worker (now as a service)
run_queue_worker() {
    local queue_name=${1:-"default"}
    
    log_info "Queue workers now run as a Cloud Run Service"
    log_info "To update queue settings, modify the service configuration"
    log_info "Current queue worker service: queue-worker-service"
    
    # Optionally show service status
    gcloud run services describe queue-worker-service --region="$REGION" --format="value(status.url)"
}

# Function to clear caches
clear_cache() {
    local rebuild=${1:-false}
    local clear_opcache=${2:-false}
    local clear_queue=${3:-false}

    local env_vars=""

    if [ "$rebuild" = "true" ]; then
        env_vars="${env_vars}REBUILD_CACHE=true,"
    fi

    if [ "$clear_opcache" = "true" ]; then
        env_vars="${env_vars}CLEAR_OPCACHE=true,"
    fi

    if [ "$clear_queue" = "true" ]; then
        env_vars="${env_vars}CLEAR_QUEUE_CACHE=true,"
    fi

    # Remove trailing comma
    env_vars=${env_vars%,}

    log_info "Clearing Laravel caches..."
    execute_job "laravel-cache-clear-job" "$env_vars"
}

# Function to run custom artisan command
run_artisan_command() {
    local command=$1
    shift
    local args="$*"
    local skip_db_check=${SKIP_DB_CHECK:-false}

    if [ -z "$command" ]; then
        log_error "No artisan command specified"
        return 1
    fi

    local env_vars="ARTISAN_COMMAND=$command"

    if [ -n "$args" ]; then
        env_vars="$env_vars,ARTISAN_ARGS=$args"
    fi

    if [ "$skip_db_check" = "true" ]; then
        env_vars="$env_vars,SKIP_DB_CHECK=true"
    fi

    log_info "Running artisan command: $command $args"
    execute_job "laravel-artisan-command-job" "$env_vars"
}

# Main script logic
main() {
    check_auth
    check_project

    case "${1:-help}" in
        "migrate")
            run_migrations "${2:-false}"
            ;;
        "migrate:seed")
            run_migrations "true"
            ;;
        "queue:work")
            run_queue_worker "$2" "$3" "$4" "$5"
            ;;
        "cache:clear")
            clear_cache "${2:-false}" "${3:-false}" "${4:-false}"
            ;;
        "cache:rebuild")
            clear_cache "true"
            ;;
        "artisan")
            shift
            run_artisan_command "$@"
            ;;
        "logs")
            get_job_logs "$2" "$3"
            ;;
        "executions")
            list_executions "$2"
            ;;
        "help"|*)
            echo "Usage: $0 <command> [options]"
            echo ""
            echo "Environment: $ENVIRONMENT"
            echo "Project: $PROJECT_ID"
            echo "Region: $REGION"
            echo ""
            echo "Commands:"
            echo "  migrate                    - Run database migrations"
            echo "  migrate:seed              - Run migrations with seeders"
            echo "  queue:work [queue] [timeout] [max_jobs] [tries] - Start queue worker"
            echo "  cache:clear [rebuild] [opcache] [queue] - Clear caches"
            echo "  cache:rebuild             - Clear and rebuild caches"
            echo "  artisan <command> [args]  - Run custom artisan command"
            echo "  logs <job_name> [execution] - Get job logs"
            echo "  executions <job_name>     - List job executions"
            echo ""
            echo "Environment Variables:"
            echo "  ENVIRONMENT               - Target environment (staging|production, default: staging)"
            echo "  PROJECT_ID                - Google Cloud Project ID"
            echo "  REGION                    - Cloud Run region (default: europe-west2)"
            echo ""
            echo "Examples:"
            echo "  $0 migrate                 # Staging (default)"
            echo "  ENVIRONMENT=production $0 migrate  # Production"
            echo "  $0 queue:work default 300 100 3"
            echo "  $0 cache:clear true true false"
            echo "  $0 artisan user:create"
            echo "  $0 artisan search:reindex User"
            echo "  $0 logs laravel-migrate-job-staging"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
