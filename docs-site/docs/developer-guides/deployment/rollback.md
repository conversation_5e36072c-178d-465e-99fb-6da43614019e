---
sidebar_position: 2
---

# Rollback Procedures

This guide covers how to rollback deployments when issues are discovered in staging or production environments.

## Rolling Back Production

If issues are discovered in production:

### 1. Immediate Rollback

Revert to previous image tag:

```bash
gcloud run services update api-service \
  --image=gcr.io/fresh-car-test/api:previous-production-tag \
  --region=europe-west2
```

### 2. Update Jobs

Update job images if needed:

```bash
./scripts/deploy-jobs.sh update-image laravel-migrate-job-production previous-tag
```

## Rolling Back Staging

```bash
gcloud run services update api-service \
  --image=gcr.io/fresh-car-test/api:previous-staging-tag \
  --region=europe-west2
```

## Post-Rollback Validation

### Staging Validation

```bash
# Health check
curl https://staging-url/health

# Test Cloud Run Jobs
./artisan-staging migrate --dry-run
```

### Production Validation

```bash
# Health check  
curl https://production-url/health

# Test Cloud Run Jobs
./artisan-production migrate --dry-run
```

## Monitoring Tools

- **Cloud Logging**: Application and job logs
- **Cloud Monitoring**: Performance metrics and alerts
- **Error Reporting**: Automatic error detection and reporting

## Troubleshooting

### Common Deployment Issues

**Build Failures:**
- Check Cloud Build logs in Google Cloud Console
- Verify all required secrets exist
- Ensure Docker build context is correct

**Service Update Failures:**
- Check IAM permissions for service account
- Verify Cloud SQL connection configuration
- Check resource limits and quotas

**Job Deployment Issues:**
- Validate YAML configuration files
- Check Cloud SQL instance connection names
- Verify service account permissions

### Getting Help

1. **Check Logs**: Cloud Build and Cloud Run logs
2. **Validate Config**: Use deployment scripts' validate commands
3. **Test Locally**: Use local development environment
4. **Contact Team**: Reach out to DevOps team for assistance

## Next Steps

After successful rollback:

1. **Monitor**: Check application metrics and logs
2. **Test**: Run automated tests against deployed environment
3. **Document**: Update any configuration changes
4. **Notify**: Inform team of rollback completion
