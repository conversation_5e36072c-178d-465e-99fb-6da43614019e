---
sidebar_position: 1
---

# Deployment Overview

This document provides comprehensive deployment instructions for the Fresh Car Core Laravel application on Google Cloud Platform.

## Architecture

The Fresh Car Core project uses Google Cloud Run for hosting the Laravel application with the following architecture:

- **Staging Environment**: Automatic deployment on code push
- **Production Environment**: Manual promotion from staging
- **Cloud Run Jobs**: For Laravel Artisan commands and background tasks
- **Cloud Run Services**: For Laravel Queue workers
- **Cloud SQL**: PostgreSQL database with private connections
- **Secret Manager**: For sensitive configuration data

## Deployment Environments

### Staging Environment

**Purpose**: Testing and validation before production deployment

- **URL**: Staging service URL (configured in Cloud Run)
- **Database**: `freshcar-staging-core` Cloud SQL instance
- **Secrets**: All secrets prefixed with `staging-`
- **Deployment**: Automatic on every push to main branch
- **Build File**: `cloudbuild.staging.yaml`

### Production Environment

**Purpose**: Live application serving real users

- **URL**: Production service URL (configured in Cloud Run)
- **Database**: `freshcar-production-core` Cloud SQL instance
- **Secrets**: All secrets prefixed with `production-`
- **Deployment**: Manual promotion from staging
- **Build File**: `cloudbuild.yaml`

## Deployment Workflow

### 1. Staging Deployment (Automatic)

When code is pushed to the main branch:

1. **Trigger**: Cloud Build automatically starts using `cloudbuild.staging.yaml`
2. **Build**: Creates Docker image with latest code
3. **Tag**: Images tagged as `staging-latest` and `$COMMIT_SHA`
4. **Deploy**: Updates Cloud Run service and jobs
5. **Test**: Validate changes in staging environment

### 2. Production Deployment (Manual)

When ready to deploy to production:

1. **Validate**: Ensure staging deployment is working correctly
2. **Trigger**: Manually run production build using `cloudbuild.yaml`
3. **Promote**: Production build pulls latest staging image
4. **Retag**: Staging image is retagged as `production-latest`
5. **Deploy**: Updates production Cloud Run service and jobs

### 3. Manual Production Deployment Commands

```bash
# Option 1: Using Cloud Build trigger
gcloud builds triggers run production-deploy-trigger

# Option 2: Direct build submission
gcloud builds submit --config=cloudbuild.yaml

# Option 3: Using Cloud Console
# Go to Cloud Build > Triggers > Run production trigger
```

## Cloud Run Jobs Integration

Both staging and production deployments automatically update Cloud Run Jobs:

### Job Types Updated

- **Migration Job**: `laravel-migrate-job-{environment}`
- **Cache Clear Job**: `laravel-cache-clear-job-{environment}`
- **Custom Command Job**: `laravel-artisan-command-job-{environment}`

### Queue Worker Service

**Names**: `laravel-queue-worker-service-{environment}`

- **Purpose**: Process queued jobs and background tasks
- **Timeout**: 1 hour
- **Resources**:
  - Staging: 1 CPU, 1Gi memory
  - Production: 2 CPU, 2Gi memory
- **Use Cases**: Email processing, data imports, background operations

### Automatic Updates

- Jobs automatically use the latest deployed image
- No manual intervention required for job updates
- Jobs pick up changes on next execution

## Environment Configuration

### Database Connections

**Staging:**

```
DB_HOST=/cloudsql/fresh-car-test:europe-west2:freshcar-staging-core
DB_DATABASE=postgres
DB_USERNAME=postgres
```

**Production:**

```
DB_HOST=/cloudsql/fresh-car-test:europe-west2:freshcar-production-core
DB_DATABASE=<database_name>
DB_USERNAME=<username>
```

### Secret Manager Integration

**Staging Secrets:**

- `staging-app-key`
- `staging-db-password`
- `staging-stripe-secret`
- `staging-dvla-*`
- `staging-gohighlevel-*`

**Production Secrets:**

- `production-app-key`
- `production-db-password`
- `production-stripe-secret`
- `production-dvla-*`
- `production-gohighlevel-*`

## Security Considerations

- All secrets stored in Secret Manager
- Service accounts use principle of least privilege
- Cloud SQL uses private IP and authorized networks
- Container images scanned for vulnerabilities
- HTTPS enforced for all external traffic
